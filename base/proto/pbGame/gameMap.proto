syntax = "proto3";

package proto;

option go_package = "world/common/pbGame;pbGame";

import "pbCross/struct.proto";

import "pbBase/Response/Response.proto";

import "pbGame/struct.proto";

//After are messages.
message S2C_EnterMapMessage {
  repeated CrossSimplePlayer plr = 1; //玩家简要数据
}
message S2C_LeaveMapMessage {
  repeated int32 id = 1; //玩家id
}
message S2C_OtherMoveMessage {
  int32 id = 1; //玩家id
  int32 x = 2; //x
  int32 y = 3; //y
  int32 mode = 4; //mode
}
message C2S_TeamJoinMessage {
  int32 otherId = 1; //其他玩家id
}
message S2C_TeamJoinMessage {
  proto.Response.Code code = 1; //响应码
}
message C2S_TeamInviteMessage {
  int32 otherId = 1; //其他玩家id
}
message S2C_TeamInviteMessage {
  proto.Response.Code code = 1; //响应码
}
message C2S_JumpMapMessage {
  int32 toMapId = 1; //目标地图id
  int32 toMapX = 2; //x
  int32 toMapY = 3; //y
}
message S2C_JumpMapMessage {
  proto.Response.Code code = 1; //响应码
}
message C2S_PlayerMoveMessage {
  Point pos = 1; //位置信息
}
message S2C_PlayerMoveMessage {
  proto.Response.Code code = 1; //
}
message S2C_ScenePlayerEventMessage {
  PlayerEvent event = 1; //
}
message C2S_PlayerEventChooseMessage {
  int32 id = 1; //事件id
  bool yes = 2; //是/否
}
message S2C_PlayerEventChooseMessage {
  proto.Response.Code code = 1; //响应码
}
message S2C_GetPlayerEventChooseResultMessage {
  int32 eventType = 1; //事件类型
  bool yes = 2; //选择结果是/否
  CrossSimplePlayer plr = 3; //玩家简要数据
}
message S2C_BroadcastTeamJoinMessage {
  int32 member = 1; //队员id
  int32 leader = 2; //队长id
}
message S2C_BroadcastTeamLeaveMessage {
  int32 member = 1; //队员id
  int32 leader = 2; //队长id
}
message C2S_ExitTeamMessage {
}
message S2C_ExitTeamMessage {
  proto.Response.Code code = 1; //响应码
}
message C2S_RemoveMemberMessage {
  int32 memberId = 1; //队员id
}
message S2C_RemoveMemberMessage {
  proto.Response.Code code = 1; //响应码
}
message C2S_ChangeLeaderMessage {
  int32 memberId = 1; //队员id
}
message S2C_ChangeLeaderMessage {
  proto.Response.Code code = 1; //响应码
}
message S2C_BroadcastChangeLeaderMessage {
  int32 oldLeader = 1; //原队长id
  int32 newLeader = 2; //新队长id
  repeated int32 members = 3; //新队员列表
}
message C2S_DisbandTeamMessage {
}
message S2C_DisbandTeamMessage {
  proto.Response.Code code = 1; //响应码
}
message S2C_BroadcastDisbandTeamMessage {
  int32 leader = 1; //原队长id
}
