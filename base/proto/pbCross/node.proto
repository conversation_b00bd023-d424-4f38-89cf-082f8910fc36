syntax = "proto3";

package proto;

option go_package = "world/common/pbCross;pbCross";

import "pbBase/Response/Response.proto";

import "pbCross/struct.proto";

//After are messages.
message S2R_OnLeaveMessage {
}
message S2R_OnPlayerLoginMessage {
}
message R2S_OnPlayerLoginMessage {
  string err = 1; //错误字符串
}
message S2R_IsPlayerOnlineMessage {
  string uid = 1; //玩家uid
}
message R2S_IsPlayerOnlineMessage {
  bool is = 1; //是否在线
}
message S2R_KickPlayerForceByPidMessage {
  string pid = 1; //玩家pid
}
message S2R_KickPlayerForceByUidMessage {
  string uid = 1; //玩家所属用户uid
}
message S2R_KickSessionByUidMessage {
  string uid = 1; //玩家所属用户uid
}
message S2R_RemoveUserByUidMessage {
  string uid = 1; //用户uid
}
message R2S_KickPlayerForceMessage {
  bool is = 1; //玩家在当前节点，并且使其离线，则返回true
}
message R2S_SimpleResponseMessage {
  proto.Response.Code code = 1; //响应码
  string data = 2; //数据
}
message S2R_NotifyPlayerEnterMapMessage {
  int32 fromMapId = 1; //之前的地图id
  int32 toMapId = 2; //要进入的地图id
  int32 toMapX = 3; //要进入的地图x
  int32 toMapY = 4; //要进入的地图y
  CrossSimplePlayer plr = 5; //玩家简要数据
}
message R2S_ReplayPlayerEnterMapMessage {
  repeated CrossSimplePlayer plr = 1; //玩家简要数据
}
message S2R_NotifyPlayerLeaveMapMessage {
  int32 mapId = 1; //地图id
  int32 id = 2; //玩家id
}
message S2R_NotifyPlayerMoveMessage {
  int32 mapId = 1; //地图id
  int32 id = 2; //玩家id
  int32 x = 3; //x
  int32 y = 4; //y
  int32 mode = 5; //mode
}
message S2R_ForwardMessage {
  string id = 1; //玩家id
  int32 gameId = 2; //玩家gameId
  string router = 3; //路由
  bytes msg = 4; //消息
}
message S2R_NotifyTeamInviteMessage {
  int32 id = 1; //邀请的玩家id
  int32 leaderId = 2; //队长id
  CrossSimplePlayer inviter = 3; //邀请者
}
message S2R_ResponseTeamInviteMessage {
  int32 to = 1; //响应者
  int32 inviter = 2; //邀请者
  int32 leaderId = 3; //队长id
}
message S2R_BroadcastJoinTeamMessage {
  int32 mapId = 1; //在哪个地图
  int32 leader = 2; //队长id
  int32 member = 3; //队员id
}
message S2R_NotifyTeamApplyMessage {
  int32 leaderId = 1; //队长id
  CrossSimplePlayer applicant = 2; //申请人
}
message S2R_ResponseTeamApplyMessage {
  int32 leaderId = 1; //队长id
  int32 applicant = 2; //申请者
}
