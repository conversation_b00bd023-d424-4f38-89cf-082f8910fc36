package gameMgr

import (
	"context"
	"time"
	"world/base/cfg"
	"world/base/enum/key"
	"world/common/pbBase/Camp"
	"world/common/pbBase/Job"
	"world/common/pbBase/ModelConst"
	"world/common/pbBase/Response"
	"world/common/pbBase/Sex"
	"world/common/pbGame"
	"world/common/pbGame/ConditionType"
	"world/common/router"
	"world/db"
	"world/game/gameBase/gameStruct"
	ut "world/utils"

	"github.com/bamzi/jobrunner"
	"github.com/huyangv/vmqant/gate"
	"github.com/samber/lo"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/huyangv/vmqant/log"
	cmap "github.com/orcaman/concurrent-map/v2"
	"github.com/sasha-s/go-deadlock"
)

var playerManagerLock deadlock.Once
var playerManager *PlayerManager

// Player 全局获取玩家管理中心
func Player() *PlayerManager {
	playerManagerLock.Do(func() {
		playerManager = &PlayerManager{
			playerMap: cmap.New[*gameStruct.Player](),
			idMap:     cmap.NewWithCustomShardingFunction[int, string](func(key int) uint32 { return uint32(key) % 100 }),
			lockMap:   cmap.New[*deadlock.Mutex](),
		}
		jobrunner.Schedule("@every 1m", playerManager)
	})
	return playerManager
}

type PlayerManager struct {
	playerMap cmap.ConcurrentMap[string, *gameStruct.Player] // 这里的key是玩家对应用户的文档id，也就是player的Id
	idMap     cmap.ConcurrentMap[int, string]                // 这里的key是玩家对应用户的文档id，也就是player的Id
	lockMap   cmap.ConcurrentMap[string, *deadlock.Mutex]    // 用户粒度的锁,这里的key是玩家对应数据的文档id，也就是player的Id
}

// AddPlayer
/*
 * @description 添加player
 * @param player
 * @return bool
 */
func (p *PlayerManager) AddPlayer(player *gameStruct.Player) bool {
	uid := player.Id
	log.Debug("AddPlayer,name: %s, gameId:%d, uid:%s", player.GetName(), player.GameId, uid)
	temp, exists := p.playerMap.Get(uid)
	if exists {
		if temp != player {
			// 如果出现这个提示，说明有bug
			log.Error("PlayerManager - AddPlayer 警告,玩家会被覆盖.playerId:%d.", player.GameId)
			return false
		}
		return true
	}
	p.playerMap.Set(uid, player)
	p.idMap.Set(player.GameId, uid)
	return true
}

// RemovePlayer 管理实例移除某个player
func (p *PlayerManager) RemovePlayer(player *gameStruct.Player) {
	log.Debug("removePlayer ,uid:%s, gameId:%d", player.Id, player.GameId)
	p.playerMap.Remove(player.Id)
	p.idMap.Remove(player.GameId)
	p.lockMap.Remove(player.Id)
}

// RemoveLockByUid
/*
 * @description 从锁map中移除
 * @param uid 玩家所属用户id
 */
func (p *PlayerManager) RemoveLockByUid(uid string) {
	p.lockMap.Remove((uid))
}

// TryGetPlayerByUid 这个id是玩家player uid
func (p *PlayerManager) TryGetPlayerByUid(uid string) (plr *gameStruct.Player, exists bool) {
	plr, exists = p.playerMap.Get(uid)
	return
}

// TryGetPlayerByGameId 这个id是玩家player gameId
func (p *PlayerManager) TryGetPlayerByGameId(gameId int) (plr *gameStruct.Player, exists bool) {
	uid, exists := p.idMap.Get(gameId)
	if !exists {
		return
	}
	plr, exists = p.playerMap.Get(uid)
	return
}

// UserKickOffline 将玩家踢下线 sendMsg=true时会给客户端发踢下线消息
func (p *PlayerManager) UserKickOffline(player *gameStruct.Player, sendMsg bool) {
	if !player.IsValid() {
		return
	}
	p.Clear(player)
	if player.IsOnline() {
		if sendMsg {
			// 踢下线
			Sender().SendTo(player, router.S2CLogoutMessage, &pbGame.S2C_LogoutMessage{
				Code: 1,
			}, true)
		}
		player.Offline()
	}
}

// UserKickOfflineByUid
/*
 * @description  将玩家踢下线
 * @param uid 玩家所属用户id
 * @param sendMsg
 * @return bool
 */
func (p *PlayerManager) UserKickOfflineByUid(uid string, sendMsg bool) bool {
	plr, exists := p.TryGetPlayerByUid(uid)
	if exists {
		p.UserKickOffline(plr, sendMsg)
	}
	return exists
}

// Clear
/*
 * @description 将玩家踢下线 sendMsg=true时会给客户端发踢下线消息
 * @param player
 */
func (p *PlayerManager) Clear(player *gameStruct.Player) {
	if !player.IsValid() {
		return
	}
	player.Save()
	player.DelNodeId()
	player.Session.Set(key.SessionPosNodeId, "")
	player.Destroy = true
	Map().LeaveMap(player)
	Team()
	p.RemovePlayer(player)
}

func (p *PlayerManager) Run() {
	log.Info("playerMap: %d,lock: %d", p.playerMap.Count(), p.lockMap.Count())

	sTime := time.Now()

	for it := range p.playerMap.IterBuffered() {
		lock := p.LockByUid(it.Key)
		plr := it.Val
		if plr.IsValid() {
			plr.Save()
		}
		ut.Unlock(lock)
	}
	log.Debug("在线玩家保存写入数据库,during :%fs", time.Since(sTime).Seconds())
}

// ClearAll 保存&清理所有玩家数据
func (p *PlayerManager) ClearAll() {
	sTime := time.Now()
	count := 0
	for item := range p.playerMap.IterBuffered() {
		plr := item.Val
		lock := p.LockByUid(plr.Id)
		p.Clear(plr)
		ut.Unlock(lock)
		count++
	}
	log.Info("Save,count: %d,during :%fs", count, time.Since(sTime).Seconds())
}

// OfflineAll 保存&清理所有玩家数据&踢下线
func (p *PlayerManager) OfflineAll() {
	sTime := time.Now()
	count := 0
	for item := range p.playerMap.IterBuffered() {
		plr := item.Val
		lock := p.LockByUid(plr.Id)
		p.UserKickOffline(plr, true)
		ut.Unlock(lock)
		count++
	}
	log.Info("Offline,count: %d,during :%fs", count, time.Since(sTime).Seconds())
}

func (p *PlayerManager) GetAll() cmap.ConcurrentMap[string, *gameStruct.Player] {
	return p.playerMap
}

// LockByUid
/*
 * @description 使用uid锁住玩家
 * @param uid 玩家所属用户id
 * @return *deadlock.Mutex
 */
func (p *PlayerManager) LockByUid(uid string) *deadlock.Mutex {
	plr, exists := p.TryGetPlayerByUid(uid)
	if exists {
		lock, exists := p.lockMap.Get(plr.Id)
		if exists {
			lock.Lock()
			return lock
		}
	}
	return p.SetLockByUid(uid)
}

// SetLockByUid
/*
 * @description 使用uid设置玩家锁，玩家不存在则会返回nil
 * @param uid 玩家所属用户id
 * @return *deadlock.Mutex
 */
func (p *PlayerManager) SetLockByUid(uid string) *deadlock.Mutex {
	plr, exists := p.TryGetPlayerByUid(uid)
	if exists {
		lock, exists := p.lockMap.Get(plr.Id)
		if !exists {
			lock = &deadlock.Mutex{}
			p.lockMap.Set(plr.Id, lock)
		}
		lock.Lock()
		return lock
	}
	return nil
}

// GetPlayerNodeId
/*
 * @description 获取player所在节点id
 * @param uid 玩家所属用户的id
 * @return string
 */
func (p *PlayerManager) GetPlayerNodeId(uid string) string {
	r := db.GetRedis().Get(context.TODO(), db.RedisKeySessionNodeId(uid))
	return r.Val()
}

func (p *PlayerManager) GetPlayerNodeIdByGameId(gameId int) string {
	r := db.GetRedis().Get(context.TODO(), db.RedisKeySessionNodeByGameId(gameId))
	return r.Val()
}

// SetPlayerNodeId
/*
 * @description 设置player所在节点id
 * @param uid 玩家所属用户的id
 * @param nodeId
 * @return error
 */
func (p *PlayerManager) SetPlayerNodeId(uid string, nodeId string) error {
	r := db.GetRedis().Set(context.TODO(), db.RedisKeySessionNodeId(uid), nodeId, 0)
	return r.Err()
}
func (p *PlayerManager) SetPlayerNodeIdByGameId(gameId int, nodeId string) error {
	r := db.GetRedis().Set(context.TODO(), db.RedisKeySessionNodeByGameId(gameId), nodeId, 0)
	return r.Err()
}

// DelPlayerNodeId
/*
 * @description 删除player所在节点id
 * @param uid 玩家所属用户的id
 * @return error
 */
func (p *PlayerManager) DelPlayerNodeId(uid string) error {
	r := db.GetRedis().Del(context.TODO(), db.RedisKeySessionNodeId(uid))
	return r.Err()
}

// CreateRole
/*
 * @description 创建角色
 * @param id 所属用户id
 * @param sex 性别
 * @param job 职业
 * @param race 阵营
 * @param name 名称
 * @param model 造型
 * @return pb.Code
 * @return *pb.SimplePlayerInfo
 */
func (p *PlayerManager) CreateRole(id string, sex Sex.Type, job Job.Type, race Camp.Type, name string, model int32) (code Response.Code, info *pbGame.SimplePlayerInfo) {
	// 检查name是否合法
	if !ut.IsValidRoleName(name) {
		return Response.ErrPlayerNameLength, nil
	}
	plr, err := gameStruct.NewPlayer(id, name, sex, job, race, model)
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return Response.ErrDuplicatePlayerName, nil
		}
		return Response.Unknown, nil
	}
	return Response.NoError, plr.ToPbSimplePlayerInfo()
}

// GetRoleList
/*
 * @description 获取角色列表
 * @param id 用户id
 * @return list 列表数据
 * @return lastRoleId 最后一次登录的角色id
 */
func (p *PlayerManager) GetRoleList(id string) (list []*pbGame.SimplePlayerInfo, lastRoleId int32) {
	cursor, err := db.PLAYER.GetCollection().Find(context.TODO(), &bson.M{
		"id": id,
	}, &options.FindOptions{
		Projection: bson.M{
			"_id":    1,
			"level":  1,
			"mapId":  1,
			"x":      1,
			"y":      1,
			"name":   1,
			"attr":   1,
			"icon1":  1,
			"icon2":  1,
			"icon3":  1,
			"gameId": 1,
			"status": 1,
		},
	})

	if err != nil {
		log.Error("[%s] GetRoleList Error %s", id, err.Error())
		return nil, 0
	}
	list = make([]*pbGame.SimplePlayerInfo, 0)
	var lastTimeFix int64 = 0
	for cursor.TryNext(context.Background()) {
		plr := &gameStruct.Player{}
		cursor.Decode(plr)
		var result bson.M
		cursor.Decode(&result)
		if lastTimeFix < plr.LastLoginTime {
			lastRoleId = int32(plr.GameId)
			lastTimeFix = plr.LastLoginTime
		}
		// 删除状态的角色不予下发
		if !plr.IsStatusBit(ModelConst.STATUS_ALL_DEL) {
			list = append(list, plr.ToPbSimplePlayerInfo())
		}
	}
	return
}

// PreloadPlayer
/*
 * @description 玩家选择角色时  预加载角色数据到内存中
 * @param id 用户id
 * @param roleId 角色id
 * @return pb.Code
 */
func (p *PlayerManager) PreloadPlayer(uid string, gameId int, session gate.Session) (code Response.Code, player *gameStruct.Player) {
	plr, err := gameStruct.TryGetPlayerFromDbByGameId(gameId)
	if err != nil {
		if mongo.ErrNoDocuments == err {
			return Response.ErrNotFoundForRoleId, nil
		}
		log.Error(err.Error())
		return Response.Unknown, nil
	}
	if plr == nil {
		return Response.Unknown, nil
	}

	if plr.Id != uid {
		return Response.ErrRoleNotBelongToCurrentUser, nil
	}

	if plr.IsStatusBit(ModelConst.STATUS_TEMP_DEL) ||
		plr.IsStatusBit(ModelConst.STATUS_OFFLINEMISSION) ||
		plr.IsStatusBit(ModelConst.STATUS_FROST) {
		return Response.ErrPlayerStatus, nil
	}

	// 更改清理标记
	plr.Destroy = false
	// 执行上线操作
	plr.Online(session)
	p.AddPlayer(plr)

	// 设置节点
	nodeId := p.GetPlayerNodeId(uid)
	p.SetPlayerNodeIdByGameId(gameId, nodeId)
	// 通知
	//Sender().SendTo(plr, router.S2CPlayerInfoMessage, &pbGame.S2C_PlayerInfoMessage{
	//	Data: plr.ToPb(),
	//})
	return Response.NoError, plr
}

// AddPlayerExp 增加经验
//
// Parameters:
//   - expVal int
//   - plr *gameStruct.GameVo
func (p *PlayerManager) AddPlayerExp(expVal int, vo gameStruct.GameVo) {
	if vo == nil || expVal <= 0 {
		return
	}
	attrMod := vo.AttrModule()

	// 增加的普通等级
	addLevel1 := 0
	// 增加的传奇等级
	addLevel2 := 0
	// 增加的属性点
	addSP := 0
	// 增加的技能点
	addCP := 0

	currentExp := func() int {
		return lo.If(attrMod.Level >= cfg.LevelMax(), attrMod.Exp2).Else(attrMod.Exp)
	}
	maxExp := func() int {
		isCq := attrMod.Level >= cfg.LevelMax()
		lv := lo.If(isCq, attrMod.Level2).Else(attrMod.Level)
		obj, _ := cfg.ContainerLevelExp.GetBeanByUnique(lv)
		// todo 传奇等级经验暂定2倍
		return lo.If(isCq, obj.Exp*2).Else(obj.Exp)
	}
	deductExp := func(exp int) {
		if attrMod.Level >= cfg.LevelMax() {
			attrMod.AddExp2(-exp)
		}
		attrMod.AddExp(-exp)
	}
	addLevel := func() {
		isCq := attrMod.Level >= cfg.LevelMax()
		if isCq {
			addLevel2 += 1
			attrMod.AddLevel2(1)
			return
		}
		attrMod.AddLevel(1)
		if attrMod.Level >= cfg.LevelMax() {
			attrMod.Level2 = 1
			attrMod.Exp2 = attrMod.Exp
			attrMod.Exp = 0
		}
		addLevel1 += 1
		addCP += cfg.ContainerMisc_C.GetObj().Role.AttributePoint
		addSP += cfg.ContainerMisc_C.GetObj().Role.SkillPoint * attrMod.Level
	}

	if attrMod.Level >= cfg.LevelMax() {
		attrMod.AddExp2(expVal)
	} else {
		attrMod.AddExp(expVal)
	}

	for currentExp() >= maxExp() {
		deductExp(maxExp())
		addLevel()
	}

	if addCP > 0 {
		p.AddValue(vo, ModelConst.CP, addCP)
	}
	if addSP > 0 {
		p.AddValue(vo, ModelConst.SP, addSP)
	}
	if addLevel1 > 0 || addLevel2 > 0 {
		gameStruct.ResumeHPMP(vo)
	}

	if pet, ok := vo.(*gameStruct.Pet); ok {
		Pet().DealPetUpgrade(pet, addLevel1)
	}
}

// IsMoneyEnough 货币是否足够
//
// Parameters:
//   - plr *gameStruct.Player
//   - moneyType int
//   - value int
//
// Returns:
//   - bool
func (p *PlayerManager) IsMoneyEnough(plr *gameStruct.Player, moneyType ModelConst.Type, value int) bool {
	switch moneyType {
	case ModelConst.MONEY1:
		return plr.Bag.Money1 >= value
	case ModelConst.MONEY2:
		return plr.Bag.Money2 >= value
	case ModelConst.MONEY3:
		return plr.Bag.Money3 >= value
	}
	return false
}

// AddValue
/*
 * @description 通用的增加玩家数值接口
 * @param vo 玩家 or 宠物
 * @param typ 类型 ModelConst
 * @param value 值
 */
func (p *PlayerManager) AddValue(vo gameStruct.GameVo, typ ModelConst.Type, value int) {
	if vo == nil {
		return
	}
	assertPlr := func() (*gameStruct.Player, bool) {
		ins, ok := vo.(*gameStruct.Player)
		return ins, ok
	}

	switch typ {
	case ModelConst.PARTNER_ID:
		if ins, ok := assertPlr(); ok {
			ins.PartnerId = value
		}
	case ModelConst.EXP:
		fallthrough
	case ModelConst.EXP2:
		p.AddPlayerExp(value, vo)
	case ModelConst.MONEY1:
		if ins, ok := assertPlr(); ok {
			Item().GrantReward(ins, gameStruct.NewCondition(ConditionType.Money1, -1, value))
		}
	case ModelConst.MONEY2:
		if ins, ok := assertPlr(); ok {
			Item().GrantReward(ins, gameStruct.NewCondition(ConditionType.Money2, -1, value))
		}
	case ModelConst.MONEY3:
		if ins, ok := assertPlr(); ok {
			Item().GrantReward(ins, gameStruct.NewCondition(ConditionType.Money3, -1, value))
		}
	case ModelConst.CP:
		vo.AttrModule().Cp += value
	case ModelConst.SP:
		vo.SkillModule().Sp += value
	}
}

// AddAttributePoint 加点
//
// Parameters:
//   - plr *gameStruct.Player
//   - info map[int32]int32 分配信息
//
// Returns:
//   - pbBase.Code
func (p *PlayerManager) AddAttributePoint(plr *gameStruct.Player, info map[ModelConst.Type]int32) Response.Code {
	attr := plr.Attr
	current := map[ModelConst.Type]int32{
		ModelConst.STR: int32(attr.Str),
		ModelConst.CON: int32(attr.Con),
		ModelConst.AGI: int32(attr.Agi),
		ModelConst.ILT: int32(attr.Ilt),
		ModelConst.WIS: int32(attr.Wis),
	}
	deduct := 0

	for typ, add := range info {
		if add < 0 {
			deduct += int(-add)
		}
		base, ok := current[typ]
		if !ok {
			// 加点属性不存在
			return Response.ErrActorAttributeType
		}
		next := base + add
		if next < 0 {
			// 加点后属性负值？
			return Response.ErrActorAttributeMinus
		}
		current[typ] = next
	}

	total := 0
	for _, v := range current {
		total += int(v)
	}

	roleCfg := cfg.ContainerMisc_C.GetObj().Role
	remain := attr.Level*roleCfg.AttributePoint - total
	if remain < 0 {
		// 客户端发送的加点总点数 超过角色拥有的点数
		return Response.ErrActorAttributeUse
	}

	if deduct > 0 {
		roleCfg := cfg.ContainerMisc_C.GetObj().Role
		cost := ut.Min(roleCfg.AttributePointChangeCost*deduct, roleCfg.AttributePointChangeCostMax)
		if !p.IsMoneyEnough(plr, ModelConst.MONEY2, cost) {
			// 货币不足
			return Response.ErrActorAttributeDeduct
		}
		p.AddValue(plr, ModelConst.MONEY2, -cost)
	}

	for typ, val := range current {
		v := int(val)
		switch typ {
		case ModelConst.STR:
			attr.Str = v
		case ModelConst.CON:
			attr.Con = v
		case ModelConst.AGI:
			attr.Agi = v
		case ModelConst.ILT:
			attr.Ilt = v
		case ModelConst.WIS:
			attr.Wis = v
		}
	}
	attr.Cp = remain

	return Response.NoError
}
