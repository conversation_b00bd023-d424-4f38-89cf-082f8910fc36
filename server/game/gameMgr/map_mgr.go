package gameMgr

import (
	"errors"
	"runtime"
	"sync"
	"world/base/cfg"
	"world/common/net_helper"
	"world/common/pbCross"
	"world/common/pbGame"
	"world/common/pb_helper"
	"world/common/router"
	"world/game/gameBase/gameStruct"
	ut "world/utils"

	"github.com/huyangv/vmqant/log"
	"github.com/huyangv/vmqant/module"
	cmap "github.com/orcaman/concurrent-map/v2"
	ants "github.com/panjf2000/ants/v2"
	"github.com/samber/lo"
	"github.com/sasha-s/go-deadlock"
	"google.golang.org/protobuf/reflect/protoreflect"
)

var mapManagerLock deadlock.Once
var mapManager *MapManager

// Map 地图
func Map() *MapManager {
	mapManagerLock.Do(func() {
		mapManager = &MapManager{}
		mapManager.dataMap = cmap.NewWithCustomShardingFunction[int, cmap.ConcurrentMap[int, *mapUnit]](func(key int) uint32 { return uint32(key) % 100 })
		var err error
		mapManager.pool, err = ants.NewPool(256, ants.WithPreAlloc(true))
		if err != nil {
			panic(err)
		}
		runtime.SetFinalizer(mapManager.pool, func(p *ants.Pool) { p.Release() })
		mapManager.loadAllMap()
	})

	return mapManager
}

type mapUnit struct {
	GameId int // 游戏id
	Leader int // 队长id
}

type MapManager struct {
	app     module.RPCModule
	pool    *ants.Pool
	dataMap cmap.ConcurrentMap[int, cmap.ConcurrentMap[int, *mapUnit]] // 外层key是地图id，内层key是玩家gameId
}

func (m *MapManager) SetApp(app module.RPCModule) { m.app = app }

// loadAllMap
/*
 * @description 加载所有地图
 */
func (m *MapManager) loadAllMap() {
	if !m.dataMap.IsEmpty() {
		log.Error("MapManager - loadMap 已经加载过地图数据了")
		return
	}
	configs := cfg.ContainerMap.GetData()
	for _, config := range configs {
		m.dataMap.Set(config.Id, cmap.NewWithCustomShardingFunction[int, *mapUnit](func(key int) uint32 { return uint32(key) % 100 }))
		log.Debug("loadMap,id:%d,name:%s", config.Id, config.Name)
		Team().initMapTeam(config.Id)
	}
}

func (m *MapManager) submitPoolTask(fn func()) error {
	if fn == nil {
		return errors.New("fn is nil")
	}
	return m.pool.Submit(fn)
}

// GetMapNpcAry
/*
 * @description 获取地图存在的npc 后续扩展npc限时出现，等操作
 * @param mapId 地图id
 * @return []int
 */
func (m *MapManager) GetMapNpcAry(mapId int) []int {
	ary := make([]int, 0)
	mapBean, _ := cfg.ContainerMap.GetBeanByUnique(mapId)
	if mapBean != nil {
		ary = lo.Keys(mapBean.NpcMap)
	}
	return ary
}

// 玩家是否在地图中
func (m *MapManager) IsInMap(mapId int, gameId int) bool {
	return m.GetUnit(mapId, gameId) != nil
}

// 获取单位
func (m *MapManager) GetUnit(mapId int, gameId int) *mapUnit {
	mapData, exists := m.dataMap.Get(mapId)
	if !exists {
		return nil
	}
	unit, _ := mapData.Get(gameId)
	return unit
}

func (m *MapManager) GetUnitByPlayer(plr *gameStruct.Player) *mapUnit {
	return m.GetUnit(plr.MapId, plr.GameId)
}

// EnterMap
/*
 * @description 进入地图
 * @param plr
 * @param toMapId 目标地图id
 * @param toMapX
 * @param tpMapY
 */
func (m *MapManager) EnterMap(plr *gameStruct.Player, toMapId, toMapX, toMapY int) {
	log.Debug("[%d]进入地图: [%d], 原地图: [%d]", plr.GameId, toMapId, plr.MapId)
	fromMapId := plr.MapId
	crossSimplePlayer := plr.ToCrossSimplePb(m.app)

	// 广播所有节点
	replay := net_helper.CallGameSync(m.app, router.S2RNotifyPlayerEnterMapMessage, &pbCross.S2R_NotifyPlayerEnterMapMessage{
		FromMapId: int32(fromMapId),
		ToMapId:   int32(toMapId),
		ToMapX:    int32(toMapX),
		ToMapY:    int32(toMapY),
		Plr:       crossSimplePlayer,
	})

	units := make([]*pbCross.CrossSimplePlayer, 0)
	for _, bytes := range replay {
		if bytes == nil {
			continue
		}
		msg := &pbCross.R2S_ReplayPlayerEnterMapMessage{}
		err := pb_helper.ProtoUnMarshal(bytes, msg)
		if err != nil {
			log.Error("EnterMap 响应解析消息失败:%v", err)
			continue
		}
		units = append(units, msg.Plr...)
	}
	// 通知客户端地图数据
	// npc基础显隐数据必须由服务器来控制
	Sender().SendTo(plr, router.S2CMapDataMessage, &pbGame.S2C_MapDataMessage{
		MapId:   int32(toMapId),
		Pos:     &pbGame.Point{X: int32(toMapX), Y: int32(toMapY)},
		NpcList: ut.ToInt32(m.GetMapNpcAry(toMapId)),
		PlrList: units,
	}, false)
}

// OnSyncPlayerEnterMap
/*
 * @description 同步玩家进入地图数据
 * @param fromMapId 原地图
 * @param toMapId 目标地图
 * @param toMapX
 * @param toMapY
 * @param plr
 */
func (m *MapManager) OnSyncPlayerEnterMap(fromMapId, toMapId, toMapX, toMapY int, plr *pbCross.CrossSimplePlayer) []*pbCross.CrossSimplePlayer {
	log.Debug("数据同步,[%d]进入地图: [%d], 原地图: [%d]", plr.GameId, toMapId, fromMapId)
	r := make([]*pbCross.CrossSimplePlayer, 0)
	sourceId := int(plr.GameId)
	if plr.Id == "" {
		log.Error("OnSyncPlayerEnterMap plr.Id is empty")
		return r
	}

	// 离开原地图
	if fromMapId != toMapId {
		mapData, _ := m.dataMap.Get(fromMapId)
		_, exists := mapData.Get(sourceId)
		if exists {
			mapData.Remove(sourceId)
			m.mapNotifyAll(fromMapId, router.S2CLeaveMapMessage, &pbGame.S2C_LeaveMapMessage{Id: plr.GameId}, sourceId)
		}
	}
	// 进入目标地图
	mapData, _ := m.dataMap.Get(toMapId)
	player, _ := Player().TryGetPlayerByGameId(sourceId)
	if player != nil {
		// 切换玩家地图数据
		player.MapId = toMapId
		player.X = toMapX
		player.Y = toMapY
	}
	mapData.Set(sourceId, &mapUnit{GameId: sourceId})

	mapData.IterCb(func(key int, v *mapUnit) {
		if v == nil {
			return
		}
		plr, exists := Player().TryGetPlayerByGameId(v.GameId)
		if !exists {
			return
		}
		if v.GameId == sourceId {
			return
		}
		r = append(r, plr.ToCrossSimplePb(m.app))
	})

	// 通知进入地图
	m.mapNotifyAll(toMapId, router.S2CEnterMapMessage, &pbGame.S2C_EnterMapMessage{Plr: plr}, sourceId)
	return r
}

// 离开当前地图
func (m *MapManager) LeaveMap(plr *gameStruct.Player) {
	mapId := plr.MapId
	mapData, _ := m.dataMap.Get(mapId)
	_, exists := mapData.Get(int(plr.GameId))
	if !exists {
		return
	}
	net_helper.CallGameSync(m.app, router.S2RNotifyPlayerLeaveMapMessage, &pbCross.S2R_NotifyPlayerLeaveMapMessage{
		MapId: int32(mapId),
		Id:    int32(plr.GameId),
	})
}

func (m *MapManager) OnSyncPlayerLeaveMap(msg *pbCross.S2R_NotifyPlayerLeaveMapMessage) {
	m.mapNotifyAll(int(msg.MapId), router.S2CLeaveMapMessage, &pbGame.S2C_LeaveMapMessage{Id: msg.Id}, int(msg.Id))
}

// 玩家移动
func (m *MapManager) PlayerMove(plr *gameStruct.Player, x, y int) {
	if plr.X == x && plr.Y == y {
		return
	}
	plr.X = x
	plr.Y = y
	net_helper.CallGameSync(m.app, router.S2RNotifyPlayerMoveMessage, &pbCross.S2R_NotifyPlayerMoveMessage{
		MapId: int32(plr.MapId),
		Id:    int32(plr.GameId),
		X:     int32(x),
		Y:     int32(y),
		Mode:  int32(plr.Mode),
	})
}
func (m *MapManager) OnSyncPlayerMove(msg *pbCross.S2R_NotifyPlayerMoveMessage) {
	m.mapNotifyAll(int(msg.MapId), router.S2COtherMoveMessage, &pbGame.S2C_OtherMoveMessage{Id: msg.Id, X: msg.X, Y: msg.Y, Mode: msg.Mode}, int(msg.Id))
}

// mapNotifyAll 通知指定地图中的所有单位
//
// Parameters:
//   - mapId int 地图id
//   - route string 路由
//   - msg protoreflect.ProtoMessage 消息数据
//   - syncSend bool 是否同步发送
//   - excludeGameId ...int 排除的玩家
func (m *MapManager) mapNotifyAll(mapId int, route string, msg protoreflect.ProtoMessage, excludeGameId ...int) {
	log.Debug("地图通知, 地图id: %d, 路由: %s, 消息: %v, 排除玩家: %v", mapId, route, msg, excludeGameId)
	if route == "" || msg == nil {
		log.Error("mapNotifyAll route or msg is nil [%s]", route)
		return
	}

	mapData, exists := m.dataMap.Get(mapId)
	if !exists {
		log.Error("mapNotifyAll mapData not exists [%s]", route)
		return
	}
	// 先收集所有需要通知的玩家
	var units []*mapUnit
	mapData.IterCb(func(key int, v *mapUnit) {
		if v != nil && !lo.Contains(excludeGameId, v.GameId) {
			units = append(units, v)
		}
	})
	var wg sync.WaitGroup
	for _, v := range units {
		if v == nil || lo.Contains(excludeGameId, v.GameId) {
			continue
		}
		wg.Add(1)
		// 注意闭包问题  =,= ...
		id := v.GameId
		err := m.submitPoolTask(func() {
			defer wg.Done()
			plr, exists := Player().TryGetPlayerByGameId(id)
			if !exists {
				return
			}
			Sender().SendTo(plr, route, msg, false)
		})
		if err != nil {
			wg.Done()
			log.Error("mapNotifyAll 提交任务到协程池失败:%v", err)
		}
	}
	wg.Wait()
}
