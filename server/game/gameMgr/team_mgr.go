package gameMgr

import (
	"context"
	"fmt"
	"world/base/enum/Define"
	"world/common/net_helper"
	"world/common/pbBase/Response"
	"world/common/pbCross"
	"world/common/pbGame"
	"world/common/pbLogin"
	"world/common/pb_helper"
	"world/common/router"
	"world/db"
	"world/game/gameBase/gameStruct"
	ut "world/utils"

	"github.com/huyangv/vmqant/log"
	"github.com/huyangv/vmqant/module"
	cmap "github.com/orcaman/concurrent-map/v2"
	"github.com/samber/lo"
	"github.com/sasha-s/go-deadlock"
)

var teamManagerLock deadlock.Once
var teamManager *TeamManager

func Team() *TeamManager {
	teamManagerLock.Do(func() {
		teamManager = &TeamManager{}
		teamManager.dataMap = cmap.NewWithCustomShardingFunction[int, cmap.ConcurrentMap[int, *teamData]](func(key int) uint32 { return uint32(key) % 100 })
	})
	return teamManager
}

type teamData struct {
	Members []*mapUnit // 队员
	Leader  *mapUnit   // 队长
	MapId   int        // 所在地图id
}

type TeamManager struct {
	app     module.RPCModule
	dataMap cmap.ConcurrentMap[int, cmap.ConcurrentMap[int, *teamData]]
}

func (t *TeamManager) SetApp(app module.RPCModule) { t.app = app }
func (t *TeamManager) initMapTeam(mapId int) {
	if t.dataMap.Count() > 0 {
		return
	}
	t.dataMap.Set(mapId, cmap.NewWithCustomShardingFunction[int, *teamData](func(key int) uint32 { return uint32(key) % 100 }))
}

// GetTeam 获取队伍数据
//
// Parameters:
//   - mapId int
//   - leader int
//
// Returns:
//   - *teamData
//   - bool
func (t *TeamManager) GetTeam(mapId, leader int) (*teamData, bool) {
	mapTeamData, exists := t.dataMap.Get(mapId)
	if !exists {
		return nil, false
	}
	teamData, exists := mapTeamData.Get(leader)
	if !exists {
		return nil, false
	}
	return teamData, true
}

// AddTeam 添加队伍数据
//
// Parameters:
//   - mapId int
//   - data *teamData
//
// Returns:
//   - bool
func (t *TeamManager) AddTeam(mapId int, data *teamData) bool {
	if data.Leader == nil {
		log.Error("添加队伍失败,队长数据不存在")
		return false
	}
	if len(data.Members) == 0 {
		log.Error("添加队伍失败,队员数据是0")
		return false
	}
	mapTeamData, exists := t.dataMap.Get(mapId)
	if !exists {
		mapTeamData = cmap.NewWithCustomShardingFunction[int, *teamData](func(key int) uint32 { return uint32(key) % 100 })
		t.dataMap.Set(mapId, mapTeamData)
	}
	mapTeamData.Set(data.Leader.GameId, data)
	return true
}

func (t *TeamManager) AddMember(mapId, leader int, member *gameStruct.Player) {
	if member == nil {
		log.Error("添加队伍成员失败,玩家数据不存在")
		return
	}
	if member.Leader != 0 && member.Leader != leader {
		log.Error("添加队伍成员失败,玩家已经在其他队伍中")
		return
	}

	data, exists := t.GetTeam(mapId, leader)
	if !exists {
		data = &teamData{
			Leader:  &mapUnit{GameId: leader},
			Members: []*mapUnit{},
		}
		t.AddTeam(mapId, data)
	}
	data.Members = append(data.Members, &mapUnit{GameId: member.GameId})
	t.AddTeam(mapId, data)
}

// 邀请加入队伍
func (t *TeamManager) DoTeamInvite(plr *gameStruct.Player, otherId int) Response.Code {
	if plr.GameId == otherId {
		return Response.E6001
	}
	// todo 先判断自己能创建队伍 ??
	mapTeamData, _ := t.dataMap.Get(plr.MapId)

	// 获取自己的队伍
	// 因为锁一致性的问题   所有的队伍消息都由队长所在节点来处理
	leaderId := lo.If(plr.IsMember(), plr.Leader).Else(plr.GameId)
	tmpTeamData, exists := mapTeamData.Get(leaderId)
	if exists {
		if len(tmpTeamData.Members) >= 4 {
			return Response.E6000
		}
	}
	if !Map().IsInMap(plr.MapId, otherId) {
		return Response.E6004
	}

	// 拿对方所在节点
	r := db.GetRedis().Get(context.TODO(), db.RedisKeySessionNodeByGameId(otherId))
	// 拿不到节点信息 说明对方不在线了
	if r.Val() == "" {
		return Response.E6001
	}
	// 给对方发消息
	bytes, err := net_helper.Invoke(t.app, r.Val(), router.S2RNotifyTeamInviteMessage, &pbCross.S2R_NotifyTeamInviteMessage{
		Id:       int32(otherId),
		LeaderId: int32(leaderId),
		Inviter:  plr.ToCrossSimplePb(t.app),
	})
	if err != nil {
		if net_helper.IsNodeNotFoundError(err) {
			return Response.E6001
		}
		log.Debug("DoTeamInvite 通知对方失败:%v", err)
	}
	msg := &pbCross.R2S_SimpleResponseMessage{}
	err = pb_helper.ProtoUnMarshal(bytes, msg)
	if err != nil {
		log.Debug("DoTeamInvite 解析消息失败:%v", err)
	}
	return msg.Code
}

func (t *TeamManager) ProcessTeamInvite(id, leaderId int, inviter *pbCross.CrossSimplePlayer) Response.Code {
	log.Info("收到组队邀请，发起者：[%d], 被邀请者：[%d], 队长id：[%d]", inviter.GameId, id, leaderId)
	// 邀请入队的消息发过来了
	plr, _ := Player().TryGetPlayerByGameId(id)
	// 拿不到玩家数据 说明离线了
	if plr == nil {
		return Response.E6001
	}
	lock := Player().LockByUid(plr.Id)
	defer ut.Unlock(lock)

	if plr.Leader != 0 {
		return Response.E6002
	}
	if plr.MapId != int(inviter.MapId) {
		return Response.E6004
	}
	plr.AddPlayerEvent(&pbCross.PlayerEvent{
		EventType:  int32(Define.PLAYER_EVENT_TEAM_INVITE),
		Message:    "",
		ExtraInfo:  fmt.Sprintf("%d", leaderId),
		ExpireTime: 60*ut.TIME_SECOND + ut.Now(), // 组队邀请 60秒后过期
		Player:     inviter,
	})
	return Response.NoError
}

// 当对方同意了入队邀请后 检查自身的组队状态
func (t *TeamManager) TeamInviteOk(to, inviter, leaderId int) {
	log.Info("[%d]同意了[%d]的入队邀请,开始检查队伍数据,队长:[%d]", to, inviter, leaderId)
	plr, _ := Player().TryGetPlayerByGameId(leaderId)
	// 消息来迟了 队长离线了
	if plr == nil {
		Sender().ForwardMsgByGameId(to, router.S2CErrorMessage, &pbLogin.S2C_ErrorMessage{Code: Response.E6001})
		return
	}
	lock := Player().LockByUid(plr.Id)
	defer ut.Unlock(lock)
	// 检查队长队伍数据状态
	data, exists := t.GetTeam(plr.MapId, leaderId)
	if !exists {
		// 队伍不存在  则创建
		data = &teamData{
			Leader:  &mapUnit{GameId: leaderId},
			Members: []*mapUnit{},
		}
		t.AddTeam(plr.MapId, data)
	}
	if len(data.Members) >= 4 {
		Sender().ForwardMsgByGameId(to, router.S2CErrorMessage, &pbLogin.S2C_ErrorMessage{Code: Response.E6005})
		return
	}

	log.Debug("广播组队邀请结果,队长:[%d],队员:[%d],地图:[%d],邀请者: [%d]", leaderId, to, plr.MapId, inviter)
	// 节点广播 因为组队数据是要同步到所有节点的
	net_helper.CallGameSync(t.app, router.S2RBroadcastJoinTeamMessage, &pbCross.S2R_BroadcastJoinTeamMessage{
		MapId:  int32(plr.MapId),
		Leader: int32(leaderId),
		Member: int32(to),
	})
}

func (t *TeamManager) OnSyncJoinTeam(mapId, leaderId, memberId int) {
	log.Debug("组队广播数据,地图:%d,队长:%d,队员:%d", mapId, leaderId, memberId)
	if leaderId == 0 || memberId == 0 || mapId == 0 {
		return
	}
	leader, _ := Player().TryGetPlayerByGameId(leaderId)
	if leader != nil {
		// 操作队长 无需加锁
		leader.Leader = leaderId
	}
	member, _ := Player().TryGetPlayerByGameId(memberId)
	if member != nil {
		// 操作队员 需要加锁
		lock := Player().LockByUid(member.Id)
		defer ut.Unlock(lock)
		member.Leader = leaderId
	}

	data, exists := t.GetTeam(mapId, leaderId)
	if !exists {
		// 队伍不存在  则创建
		data = &teamData{
			Leader: &mapUnit{GameId: leaderId},
		}
		t.AddTeam(mapId, data)
	}

	if data.Members == nil {
		data.Members = []*mapUnit{}
	}
	// 添加队员
	data.Members = append(data.Members, &mapUnit{GameId: memberId})
	// 通知客户端
	msg := &pbGame.S2C_BroadcastTeamJoinMessage{
		Leader: int32(leaderId),
		Member: int32(memberId),
	}
	if leader != nil {
		Sender().SendTo(leader, router.S2CBroadcastTeamJoinMessage, msg, true)
	}
	if member != nil {
		Sender().SendTo(member, router.S2CBroadcastTeamJoinMessage, msg, true)
	}
}

// 申请加入队伍
func (t *TeamManager) ApplyJoinTeam(plr *gameStruct.Player, leaderId int) Response.Code {
	if leaderId == 0 || plr.GameId == leaderId {
		return Response.E6001
	}
	if plr.Leader != 0 {
		return Response.E6003
	}
	mapTeamData, _ := t.dataMap.Get(plr.MapId)
	tmpTeamData, exists := mapTeamData.Get(leaderId)
	if exists {
		if len(tmpTeamData.Members) >= 4 {
			return Response.E6000
		}
	}
	if !Map().IsInMap(plr.MapId, leaderId) {
		return Response.E6004
	}
	// 拿对方所在节点
	r := db.GetRedis().Get(context.TODO(), db.RedisKeySessionNodeByGameId(leaderId))
	// 拿不到节点信息 说明对方不在线了
	if r.Val() == "" {
		return Response.E6001
	}
	// 给对方发消息
	bytes, err := net_helper.Invoke(t.app, r.Val(), router.S2RNotifyTeamApplyMessage, &pbCross.S2R_NotifyTeamApplyMessage{
		LeaderId:  int32(leaderId),
		Applicant: plr.ToCrossSimplePb(t.app),
	})
	if err != nil {
		if net_helper.IsNodeNotFoundError(err) {
			return Response.E6001
		}
		log.Debug("申请入队时 通知队长失败:%v", err)
	}
	msg := &pbCross.R2S_SimpleResponseMessage{}
	err = pb_helper.ProtoUnMarshal(bytes, msg)
	if err != nil {
		log.Debug("申请入队时 解析消息失败:%v", err)
	}
	return msg.Code
}

func (t *TeamManager) ProcessTeamApply(leaderId int, applicant *pbCross.CrossSimplePlayer) Response.Code {
	log.Info("收到组队申请，发起者：[%d], 队长id：[%d]", applicant.GameId, leaderId)

	plr, _ := Player().TryGetPlayerByGameId(leaderId)
	// 拿不到玩家数据 说明离线了
	if plr == nil {
		return Response.E6001
	}
	lock := Player().LockByUid(plr.Id)
	defer ut.Unlock(lock)
	// 两个人要在同一个地图
	if plr.MapId != int(applicant.MapId) {
		return Response.E6004
	}
	plr.AddPlayerEvent(&pbCross.PlayerEvent{
		EventType:  int32(Define.PLAYER_EVENT_TEAM_APPLY),
		Message:    "",
		ExtraInfo:  fmt.Sprintf("%d", leaderId),
		ExpireTime: 60*ut.TIME_SECOND + ut.Now(), // 组队申请 60秒后过期
		Player:     applicant,
	})
	return Response.NoError
}

// 当对方同意了入队申请后 检查自身的组队状态
func (t *TeamManager) TeamApplyOk(applicant, leaderId int) {
	log.Info("[%d]同意了[%d]的入队申请,开始检查队伍数据", leaderId, applicant)

	// 队长
	plr, _ := Player().TryGetPlayerByGameId(leaderId)
	// 消息来迟了 队长离线了
	if plr == nil {
		Sender().ForwardMsgByGameId(applicant, router.S2CErrorMessage, &pbLogin.S2C_ErrorMessage{Code: Response.E6001})
		return
	}
	lock := Player().LockByUid(plr.Id)
	defer ut.Unlock(lock)
	// 检查队长队伍数据状态
	data, exists := t.GetTeam(plr.MapId, leaderId)
	if !exists {
		// 队伍不存在  则创建
		data = &teamData{
			Leader:  &mapUnit{GameId: leaderId},
			Members: []*mapUnit{},
		}
		t.AddTeam(plr.MapId, data)
	}

}
