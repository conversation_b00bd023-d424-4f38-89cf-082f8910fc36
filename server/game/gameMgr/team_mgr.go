package gameMgr

import (
	"context"
	"fmt"
	"world/base/enum/Define"
	"world/common/net_helper"
	"world/common/pbBase/Response"
	"world/common/pbCross"
	"world/common/pbGame"
	"world/common/pbLogin"
	"world/common/pb_helper"
	"world/common/router"
	"world/db"
	"world/game/gameBase/gameStruct"
	ut "world/utils"

	"github.com/huyangv/vmqant/log"
	"github.com/huyangv/vmqant/module"
	cmap "github.com/orcaman/concurrent-map/v2"
	"github.com/samber/lo"
	"github.com/sasha-s/go-deadlock"
)

var teamManagerLock deadlock.Once
var teamManager *TeamManager

func Team() *TeamManager {
	teamManagerLock.Do(func() {
		teamManager = &TeamManager{}
		teamManager.dataMap = cmap.NewWithCustomShardingFunction[int, cmap.ConcurrentMap[int, *teamData]](func(key int) uint32 { return uint32(key) % 100 })
	})
	return teamManager
}

type teamData struct {
	Members []*mapUnit // 队员
	Leader  *mapUnit   // 队长
	MapId   int        // 所在地图id
}

type TeamManager struct {
	app     module.RPCModule
	dataMap cmap.ConcurrentMap[int, cmap.ConcurrentMap[int, *teamData]]
}

func (t *TeamManager) SetApp(app module.RPCModule) { t.app = app }
func (t *TeamManager) initMapTeam(mapId int) {
	_, exists := t.dataMap.Get(mapId)
	if exists {
		log.Error("initMapTeam 地图id:%d 已经初始化过了", mapId)
		return
	}
	t.dataMap.Set(mapId, cmap.NewWithCustomShardingFunction[int, *teamData](func(key int) uint32 { return uint32(key) % 100 }))
}

// GetTeam 获取队伍数据
//
// Parameters:
//   - mapId int
//   - leader int
//
// Returns:
//   - *teamData
//   - bool
func (t *TeamManager) GetTeam(mapId, leader int) (*teamData, bool) {
	mapTeamData, exists := t.dataMap.Get(mapId)
	if !exists {
		return nil, false
	}
	teamData, exists := mapTeamData.Get(leader)
	if !exists {
		return nil, false
	}
	return teamData, true
}

func (t *TeamManager) IsMember(plr *gameStruct.Player) bool {
	unit := Map().GetUnitByPlayer(plr)
	if unit == nil {
		log.Error("为什么在当前节点仍然获取不到玩家单位？")
		return false
	}
	return unit.Leader > 0
}

func (t *TeamManager) IsLeader(plr *gameStruct.Player) bool {
	unit := Map().GetUnitByPlayer(plr)
	if unit == nil {
		log.Error("为什么在当前节点仍然获取不到玩家单位？")
		return false
	}
	return unit.Leader == plr.GameId
}

// AddTeam 添加队伍数据
//
// Parameters:
//   - mapId int
//   - data *teamData
//
// Returns:
//   - bool
func (t *TeamManager) AddTeam(mapId int, data *teamData) bool {
	if data.Leader == nil {
		log.Error("添加队伍失败,队长数据不存在")
		return false
	}
	mapTeamData, exists := t.dataMap.Get(mapId)
	if !exists {
		mapTeamData = cmap.NewWithCustomShardingFunction[int, *teamData](func(key int) uint32 { return uint32(key) % 100 })
		t.dataMap.Set(mapId, mapTeamData)
	}
	mapTeamData.Set(data.Leader.GameId, data)
	return true
}

func (t *TeamManager) DelTeam(mapId, leader int) {
	mapTeamData, exists := t.dataMap.Get(mapId)
	if !exists {
		return
	}
	mapTeamData.Remove(leader)
}

// 邀请加入队伍
func (t *TeamManager) DoTeamInvite(plr *gameStruct.Player, otherId int) Response.Code {
	if plr.GameId == otherId {
		return Response.E6001
	}
	// todo 先判断自己能创建队伍 ??
	mapTeamData, _ := t.dataMap.Get(plr.MapId)

	// 获取自己的队伍
	// 因为锁一致性的问题   所有的队伍消息都由队长所在节点来处理
	unit := Map().GetUnitByPlayer(plr)
	leaderId := unit.Leader
	if leaderId == 0 {
		leaderId = plr.GameId
	}
	tmpTeamData, exists := mapTeamData.Get(leaderId)
	if exists {
		if len(tmpTeamData.Members) >= 4 {
			return Response.E6000
		}
	}
	if !Map().IsInMap(plr.MapId, otherId) {
		return Response.E6004
	}

	// 拿对方所在节点
	r := db.GetRedis().Get(context.TODO(), db.RedisKeySessionNodeByGameId(otherId))
	// 拿不到节点信息 说明对方不在线了
	if r.Val() == "" {
		return Response.E6001
	}
	// 给对方发消息
	bytes, err := net_helper.Invoke(t.app, r.Val(), router.S2RNotifyTeamInviteMessage, &pbCross.S2R_NotifyTeamInviteMessage{
		Id:       int32(otherId),
		LeaderId: int32(leaderId),
		Inviter:  plr.ToCrossSimplePb(t.app),
	})
	if err != nil {
		if net_helper.IsNodeNotFoundError(err) {
			return Response.E6001
		}
		log.Debug("DoTeamInvite 通知对方失败:%v", err)
	}
	msg := &pbCross.R2S_SimpleResponseMessage{}
	err = pb_helper.ProtoUnMarshal(bytes, msg)
	if err != nil {
		log.Debug("DoTeamInvite 解析消息失败:%v", err)
	}
	return msg.Code
}

func (t *TeamManager) ProcessTeamInvite(otherId, leaderId int, inviter *pbCross.CrossSimplePlayer) Response.Code {
	log.Info("收到组队邀请，发起者：[%d], 被邀请者：[%d], 队长id：[%d]", inviter.GameId, otherId, leaderId)
	// 邀请入队的消息发过来了
	plr, _ := Player().TryGetPlayerByGameId(otherId)
	// 拿不到玩家数据 说明离线了
	if plr == nil {
		return Response.E6001
	}
	lock := Player().LockByUid(plr.Id)
	defer ut.Unlock(lock)

	plrUnit := Map().GetUnitByPlayer(plr)
	if plrUnit.Leader != 0 {
		return Response.E6002
	}
	if plr.MapId != int(inviter.MapId) {
		return Response.E6004
	}
	plr.AddPlayerEvent(&pbCross.PlayerEvent{
		EventType:  int32(Define.PLAYER_EVENT_TEAM_INVITE),
		Message:    "",
		ExtraInfo:  fmt.Sprintf("%d", leaderId),
		ExpireTime: 60*ut.TIME_SECOND + ut.Now(), // 组队邀请 60秒后过期
		Player:     inviter,
	})
	return Response.NoError
}

// 当对方同意了入队邀请后 检查自身的组队状态
func (t *TeamManager) TeamInviteOk(to, inviter, leaderId int) {
	log.Info("[%d]同意了[%d]的入队邀请,开始检查队伍数据,队长:[%d]", to, inviter, leaderId)
	plr, _ := Player().TryGetPlayerByGameId(leaderId)
	// 消息来迟了 队长离线了
	if plr == nil {
		Sender().ForwardMsgByGameId(to, router.S2CErrorMessage, &pbLogin.S2C_ErrorMessage{Code: Response.E6001})
		return
	}
	lock := Player().LockByUid(plr.Id)
	defer ut.Unlock(lock)
	// 检查队长队伍数据状态
	data, exists := t.GetTeam(plr.MapId, leaderId)
	if exists && len(data.Members) >= 4 {
		Sender().ForwardMsgByGameId(to, router.S2CErrorMessage, &pbLogin.S2C_ErrorMessage{Code: Response.E6005})
		return
	}

	log.Debug("广播组队邀请结果,队长:[%d],队员:[%d],地图:[%d],邀请者: [%d]", leaderId, to, plr.MapId, inviter)
	// 节点广播 因为组队数据是要同步到所有节点的
	net_helper.CallGameSync(t.app, router.S2RBroadcastJoinTeamMessage, &pbCross.S2R_BroadcastJoinTeamMessage{
		MapId:      int32(plr.MapId),
		Leader:     int32(leaderId),
		Member:     int32(to),
		LockMember: false,
	})
}

func (t *TeamManager) OnSyncJoinTeam(mapId, leaderId, memberId int, lockMember bool) {
	log.Debug("组队广播数据,地图:%d,队长:%d,队员:%d", mapId, leaderId, memberId)
	if leaderId == 0 || memberId == 0 || mapId == 0 {
		return
	}
	leader, _ := Player().TryGetPlayerByGameId(leaderId)
	if leader != nil {
	}

	data, exists := t.GetTeam(mapId, leaderId)
	if !exists {
		// 队伍不存在  则创建
		data = &teamData{}
		data.Leader = Map().GetUnit(mapId, leaderId)
		data.Leader.Leader = leaderId
		t.AddTeam(mapId, data)
	}

	if data.Members == nil {
		data.Members = []*mapUnit{}
	}
	_, exists = lo.Find(data.Members, func(member *mapUnit) bool { return member.GameId == memberId })
	if exists {
		return
	}
	// 添加队员
	memberUnit := Map().GetUnit(mapId, memberId)
	data.Members = append(data.Members, memberUnit)

	member, _ := Player().TryGetPlayerByGameId(memberId)
	if member != nil {
		if lockMember {
			lock := Player().LockByUid(member.Id)
			defer ut.Unlock(lock)
		}
		memberUnit.Leader = leaderId
	}

	// 通知客户端
	msg := &pbGame.S2C_BroadcastTeamJoinMessage{
		Leader: int32(leaderId),
		Member: int32(memberId),
	}
	Map().mapNotifyAll(mapId, router.S2CBroadcastTeamJoinMessage, msg)
}

// 申请加入队伍
func (t *TeamManager) ApplyJoinTeam(plr *gameStruct.Player, leaderId int) Response.Code {
	if leaderId == 0 || plr.GameId == leaderId {
		return Response.E6001
	}
	plrUnit := Map().GetUnitByPlayer(plr)
	if plrUnit.Leader != 0 {
		return Response.E6003
	}

	unit := Map().GetUnit(plr.MapId, leaderId)
	if unit == nil {
		return Response.E6004
	}
	if unit.Leader != 0 && unit.Leader != leaderId {
		leaderId = unit.Leader
	}

	// 看一下原队伍数据
	mapTeamData, _ := t.dataMap.Get(plr.MapId)
	tmpTeamData, exists := mapTeamData.Get(leaderId)
	if exists {
		if len(tmpTeamData.Members) >= 4 {
			return Response.E6000
		}
	}

	if !Map().IsInMap(plr.MapId, leaderId) {
		return Response.E6004
	}
	// 队长所在节点
	r := db.GetRedis().Get(context.TODO(), db.RedisKeySessionNodeByGameId(leaderId))
	// 拿不到节点信息 说明对方不在线了
	if r.Val() == "" {
		return Response.E6001
	}
	// 给队长发消息
	bytes, err := net_helper.Invoke(t.app, r.Val(), router.S2RNotifyTeamApplyMessage, &pbCross.S2R_NotifyTeamApplyMessage{
		LeaderId:  int32(leaderId),
		Applicant: plr.ToCrossSimplePb(t.app),
	})
	if err != nil {
		if net_helper.IsNodeNotFoundError(err) {
			return Response.E6001
		}
		log.Debug("申请入队时 通知队长失败:%v", err)
	}
	msg := &pbCross.R2S_SimpleResponseMessage{}
	err = pb_helper.ProtoUnMarshal(bytes, msg)
	if err != nil {
		log.Debug("申请入队时 解析消息失败:%v", err)
	}
	return msg.Code
}

func (t *TeamManager) ProcessTeamApply(leaderId int, applicant *pbCross.CrossSimplePlayer) Response.Code {
	log.Info("收到组队申请，发起者：[%d], 队长id：[%d]", applicant.GameId, leaderId)

	plr, _ := Player().TryGetPlayerByGameId(leaderId)
	// 拿不到玩家数据 说明离线了
	if plr == nil {
		return Response.E6001
	}
	lock := Player().LockByUid(plr.Id)
	defer ut.Unlock(lock)
	// 两个人要在同一个地图
	if plr.MapId != int(applicant.MapId) {
		return Response.E6004
	}
	plr.AddPlayerEvent(&pbCross.PlayerEvent{
		EventType:  int32(Define.PLAYER_EVENT_TEAM_APPLY),
		Message:    "",
		ExtraInfo:  fmt.Sprintf("%d", leaderId),
		ExpireTime: 60*ut.TIME_SECOND + ut.Now(), // 组队申请 60秒后过期
		Player:     applicant,
	})
	return Response.NoError
}

// 当对方同意了入队申请后 检查自身的组队状态
func (t *TeamManager) TeamApplyOk(applicant, leaderId int) {
	log.Info("[%d]同意了[%d]的入队申请,开始检查队伍数据", leaderId, applicant)

	// 队长
	plr, _ := Player().TryGetPlayerByGameId(leaderId)
	// 消息来迟了 队长离线了
	if plr == nil {
		Sender().ForwardMsgByGameId(applicant, router.S2CErrorMessage, &pbLogin.S2C_ErrorMessage{Code: Response.E6001})
		return
	}
	// 检查队长队伍数据状态
	data, exists := t.GetTeam(plr.MapId, leaderId)
	if exists && len(data.Members) >= 4 {
		Sender().ForwardMsgByGameId(applicant, router.S2CErrorMessage, &pbLogin.S2C_ErrorMessage{Code: Response.E6005})
		return
	}
	log.Debug("广播组队邀请申请,队长:[%d],队员:[%d],地图:[%d]", leaderId, applicant, plr.MapId)
	// 节点广播 因为组队数据是要同步到所有节点的
	net_helper.CallGameSync(t.app, router.S2RBroadcastJoinTeamMessage, &pbCross.S2R_BroadcastJoinTeamMessage{
		MapId:      int32(plr.MapId),
		Leader:     int32(leaderId),
		Member:     int32(applicant),
		LockMember: true,
	})
}

// 玩家要退出队伍  由队长所在节点来处理
func (t *TeamManager) ExitTeam(plr *gameStruct.Player, offline bool) Response.Code {
	if !t.IsMember(plr) {
		return Response.E6006
	}
	if t.IsLeader(plr) {
		if offline {
			t.DisbandTeam(plr)
			return Response.NoError
		}
		return Response.E6010
	}
	unit := Map().GetUnitByPlayer(plr)
	_, exists := t.GetTeam(plr.MapId, unit.Leader)
	if !exists {
		// 可能是不同步导致的  记录一下
		log.Debug("退出队伍时发现数据不一致,玩家没在队伍中,需要检查.")
		return Response.NoError
	}

	Sender().SendMsgToNodeByPlayerId("", unit.Leader, router.S2RNotifyExitTeamMessage, &pbCross.S2R_NotifyExitTeamMessage{
		MapId:  int32(plr.MapId),
		Leader: int32(unit.Leader),
		Member: int32(unit.GameId),
	}, 1)

	return Response.NoError
}

func (t *TeamManager) ProcessTeamExit(mapId, leaderId, member int) {
	// 队长
	plr, _ := Player().TryGetPlayerByGameId(leaderId)
	// 消息来迟了 队长离线了
	if plr == nil {
		return
	}
	if leaderId != member {
		lock := Player().LockByUid(plr.Id)
		defer ut.Unlock(lock)
	}
	log.Debug("玩家退出队伍,队长:[%d],队员:[%d]", leaderId, member)
	// 节点广播 因为组队数据是要同步到所有节点的
	net_helper.CallGameSync(t.app, router.S2RBroadcastExitTeamMessage, &pbCross.S2R_BroadcastExitTeamMessage{
		MapId:      int32(plr.MapId),
		Leader:     int32(leaderId),
		Member:     int32(member),
		LockMember: false,
	})
}

func (t *TeamManager) OnSyncExitTeam(mapId, leaderId, memberId int, lockMember bool) {
	log.Debug("退出组队广播,地图:%d,队长:%d,队员:%d", mapId, leaderId, memberId)
	if leaderId == 0 || memberId == 0 || mapId == 0 {
		return
	}

	msg := &pbGame.S2C_BroadcastTeamLeaveMessage{
		Leader: int32(leaderId),
		Member: int32(memberId),
	}
	data, exists := t.GetTeam(mapId, leaderId)
	if !exists {
		log.Error("退出组队广播时发现数据不同步")
		return
	}
	member, _ := Player().TryGetPlayerByGameId(memberId)
	if member != nil {
		if lockMember {
			lock := Player().LockByUid(member.Id)
			defer ut.Unlock(lock)
		}
		unit := Map().GetUnit(mapId, memberId)
		unit.Leader = 0
	}

	Map().mapNotifyAll(mapId, router.S2CBroadcastTeamLeaveMessage, msg)
	// 过滤队员
	after := lo.Filter(data.Members, func(mu *mapUnit, i int) bool { return mu != nil && mu.GameId != memberId })
	if len(after) == len(data.Members) {
		log.Error("退出组队，为什么过滤后长度还是一样？？")
	}
	data.Members = after
	// 如果没有队员了 就解散队伍
	if len(after) == 0 {
		t.DelTeam(mapId, leaderId)
		unit := Map().GetUnit(mapId, leaderId)
		unit.Leader = 0
		return
	}
}

// 踢出队员
func (t *TeamManager) RemoveMember(plr *gameStruct.Player, memberId int) Response.Code {
	if !t.IsMember(plr) {
		return Response.E6008
	}
	// 只有队长才能操作
	if !t.IsLeader(plr) {
		return Response.E6007
	}

	leaderId := plr.GameId
	// 判断队员在不在
	data, exists := t.GetTeam(plr.MapId, leaderId)
	if !exists {
		return Response.E6008
	}
	_, index, _ := lo.FindIndexOf(data.Members, func(mu *mapUnit) bool { return mu.GameId == memberId })
	if index == -1 {
		return Response.E6009
	}
	log.Debug("玩家被踢出队伍,队长:[%d],队员:[%d]", leaderId, memberId)
	// 节点广播 因为组队数据是要同步到所有节点的
	net_helper.CallGameSync(t.app, router.S2RBroadcastExitTeamMessage, &pbCross.S2R_BroadcastExitTeamMessage{
		MapId:      int32(plr.MapId),
		Leader:     int32(leaderId),
		Member:     int32(memberId),
		LockMember: true,
	})

	return Response.NoError
}

// 转移队长
func (t *TeamManager) ChangeLeader(plr *gameStruct.Player, memberId int) Response.Code {
	if !t.IsMember(plr) {
		return Response.E6008
	}
	// 只有队长才能操作
	if !t.IsLeader(plr) {
		return Response.E6007
	}
	leaderId := plr.GameId
	// 判断队员在不在
	data, exists := t.GetTeam(plr.MapId, leaderId)
	if !exists {
		return Response.E6008
	}
	_, index, _ := lo.FindIndexOf(data.Members, func(mu *mapUnit) bool { return mu.GameId == memberId })
	if index == -1 {
		return Response.E6009
	}
	log.Debug("转移队长,原队长:[%d],新队长:[%d]", leaderId, memberId)
	net_helper.CallGameSync(t.app, router.S2RBroadcastChangeLeaderMessage, &pbCross.S2R_BroadcastChangeLeaderMessage{
		MapId:  int32(plr.MapId),
		Leader: int32(leaderId),
		Member: int32(memberId),
	})
	return Response.NoError
}

func (t *TeamManager) OnSyncChangeLeader(mapId, leaderId, memberId int) {
	log.Debug("改变队长广播,地图:%d,原队长:%d,新队长:%d", mapId, leaderId, memberId)
	if leaderId == 0 || memberId == 0 || mapId == 0 {
		return
	}
	data, exists := t.GetTeam(mapId, leaderId)
	if !exists {
		log.Error("改变队长广播时发现数据不同步")
		return
	}
	memberPlayer, _ := Player().TryGetPlayerByGameId(memberId)
	if memberPlayer != nil {
		lock := Player().LockByUid(memberPlayer.Id)
		defer ut.Unlock(lock)
	}
	newLeader := Map().GetUnit(mapId, memberId)
	oldLeader := Map().GetUnit(mapId, leaderId)
	// 临时新的队伍数据
	newTeamData := &teamData{
		Leader:  newLeader,
		Members: []*mapUnit{oldLeader},
	}
	// 更换队长标识
	newLeader.Leader = memberId
	oldLeader.Leader = memberId
	lo.ForEach(data.Members, func(mu *mapUnit, i int) {
		// 之前的队员 变成了队长
		if mu.GameId == memberId {
			return
		}
		mu.Leader = memberId
		newTeamData.Members = append(newTeamData.Members, mu)
	})
	msg := &pbGame.S2C_BroadcastChangeLeaderMessage{
		OldLeader: int32(leaderId),
		NewLeader: int32(memberId),
		Members:   lo.Map(newTeamData.Members, func(mu *mapUnit, i int) int32 { return int32(mu.GameId) }),
	}
	leader, _ := Player().TryGetPlayerByGameId(leaderId)
	if leader != nil {
		unit := Map().GetUnit(mapId, leaderId)
		unit.Leader = memberId
	}
	// 给原来的队伍成员发送
	for _, unit := range data.Members {
		if unit.GameId == memberId {
			continue
		}
		member, _ := Player().TryGetPlayerByGameId(unit.GameId)
		if member == nil {
			continue
		}
		/*
			-潜在的死锁
			-如果队伍有队员A、B、C
			-在循环中会依次获取A、B、C的锁
			-如果同时有其他操作按C、B、A的顺序获取锁，就可能死锁
		*/
		// 更新每个队员的队长id
		lock := Player().LockByUid(member.Id)
		defer ut.Unlock(lock)
		unit := Map().GetUnit(mapId, member.GameId)
		unit.Leader = memberId
	}
	Map().mapNotifyAll(mapId, router.S2CBroadcastChangeLeaderMessage, msg)
	mapTeamData, _ := t.dataMap.Get(mapId)
	// 移除旧队伍
	mapTeamData.Remove(leaderId)
	// 添加新队伍
	mapTeamData.Set(memberId, newTeamData)
}

// DisbandTeam 解散队伍
func (t *TeamManager) DisbandTeam(plr *gameStruct.Player) Response.Code {
	if !t.IsMember(plr) {
		return Response.E6008
	}
	if !t.IsLeader(plr) {
		return Response.E6007
	}
	_, exists := t.GetTeam(plr.MapId, plr.GameId)
	if !exists {
		return Response.E6008
	}
	log.Debug("DisbandTeam 解散队伍,队长:[%d]", plr.GameId)
	net_helper.CallGameSync(t.app, router.S2RBroadcastDisbandTeamMessage, &pbCross.S2R_BroadcastDisbandTeamMessage{
		MapId:  int32(plr.MapId),
		Leader: int32(plr.GameId),
	})
	return Response.NoError
}

func (t *TeamManager) OnSyncDisbandTeam(mapId, leaderId int) {
	log.Debug("OnSyncDisbandTeam 解散队伍,队长:[%d]", leaderId)
	if leaderId == 0 || mapId == 0 {
		return
	}
	data, exists := t.GetTeam(mapId, leaderId)
	if !exists {
		log.Error("OnSyncDisbandTeam 解散队伍时发现数据不同步")
		return
	}
	msg := &pbGame.S2C_BroadcastDisbandTeamMessage{Leader: int32(leaderId)}

	leader, _ := Player().TryGetPlayerByGameId(leaderId)
	if leader != nil {
		unit := Map().GetUnit(mapId, leaderId)
		unit.Leader = 0
	}
	for _, unit := range data.Members {
		member, _ := Player().TryGetPlayerByGameId(unit.GameId)
		if member == nil {
			continue
		}
		lock := Player().LockByUid(member.Id)
		defer ut.Unlock(lock)
		unit := Map().GetUnit(mapId, member.GameId)
		unit.Leader = 0
	}
	Map().mapNotifyAll(mapId, router.S2CBroadcastDisbandTeamMessage, msg)
	t.DelTeam(mapId, leaderId)
}
