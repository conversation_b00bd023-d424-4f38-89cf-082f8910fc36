package gameMap

import (
	"world/base/enum/Define"
	"world/common/net_helper"
	"world/common/pbBase/Response"
	"world/common/pbCross"
	"world/common/pbGame"
	"world/common/router"
	"world/game/gameBase/gameStruct"
	"world/game/gameMgr"
	ut "world/utils"

	"github.com/huyangv/vmqant/log"
	"github.com/huyangv/vmqant/module"
	"github.com/spf13/cast"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// C2sPlayerEventChooseMessageHandler 玩家对于场景事件的选择
func C2sPlayerEventChooseMessageHandler(this module.RPCModule) any {
	/* action-code-start */
	return func(player *gameStruct.Player, msg *pbGame.C2S_PlayerEventChooseMessage) protoreflect.ProtoMessage {

		id := int(msg.GetId())
		yes := msg.GetYes()
		code := handlePlayerEventChoose(this, player, id, yes)
		return &pbGame.S2C_PlayerEventChooseMessage{Code: code}
	}
	/* action-code-end */
}

/* logic-code-start */

func handlePlayerEventChoose(app module.RPCModule, player *gameStruct.Player, eventId int, yes bool) Response.Code {
	evt := player.GetPlayerEvent(eventId, true)
	if evt == nil || evt.ExpireTime < ut.Now() {
		return Response.E22
	}

	code := Response.NoError
	switch evt.EventType {
	case int32(Define.PLAYER_EVENT_TEAM_INVITE):
		code = teamInvite(app, player, yes, evt)
	case int32(Define.PLAYER_EVENT_TEAM_APPLY):
		code = teamApply(app, player, yes, evt)
	}

	// 处理逻辑没有错误 通知事件发起者 事件选择的结果
	if code == Response.NoError {
		msg := &pbGame.S2C_GetPlayerEventChooseResultMessage{EventType: int32(evt.EventType), Yes: yes, Plr: player.ToCrossSimplePb(app)}
		gameMgr.Sender().ForwardMsgById(evt.Player.Id, router.S2CGetPlayerEventChooseResultMessage, msg)
	}

	return code
}

// 邀请入队
func teamInvite(app module.RPCModule, player *gameStruct.Player, yes bool, evt *pbCross.PlayerEvent) Response.Code {
	if !yes {
		return Response.NoError
	}
	// 同意邀请
	// 但是自己已经加入了某个队伍
	if player.Leader != 0 {
		return Response.E6003
	}
	// 双方不在同一个地图了
	if !gameMgr.Map().IsInMap(player.MapId, int(evt.Player.GameId)) {
		return Response.E6004
	}
	// 发消息给对方 添加队员
	leaderId := cast.ToInt(evt.ExtraInfo)
	if leaderId == 0 {
		// ???
		return Response.Unknown
	}
	log.Info("同意邀请入队，邀请者：[%d], 被邀请者：[%d], 队长id: [%d]", evt.Player.GameId, player.GameId, leaderId)

	// 这里异步发送，因为当前堆栈持有被邀请者的锁
	// 而ResponseTeamInvite会通知被邀请者和队员，可能会造成二次锁竞争，所以这里异步发送后立即解释上下文
	// 而且这个消息必须发给队长，因为队长是唯一的，可以用来做基本锁
	err := gameMgr.Sender().AsyncSendMsgToNodeByPlayerId("", leaderId, router.S2RResponseTeamInviteMessage, &pbCross.S2R_ResponseTeamInviteMessage{To: int32(player.GameId), Inviter: evt.Player.GameId, LeaderId: int32(leaderId)}, 1)
	if net_helper.IsNodeNotFoundError(err) {
		// 消息来迟了 邀请者离线了
		return Response.E6001
	}
	return Response.NoError
}

func teamApply(app module.RPCModule, player *gameStruct.Player, yes bool, evt *pbCross.PlayerEvent) Response.Code {
	if !yes {
		return Response.NoError
	}
	// 同意入队申请
	// 双方不在同一个地图了
	if !gameMgr.Map().IsInMap(player.MapId, int(evt.Player.GameId)) {
		return Response.E6004
	}
	leaderId := cast.ToInt(evt.ExtraInfo)
	if leaderId == 0 {
		return Response.Unknown
	}
	log.Info("同意入队申请，申请者：[%d], 队长id: [%d]", evt.Player.GameId, leaderId)
	err := gameMgr.Sender().AsyncSendMsgToNodeByPlayerId("", leaderId, router.S2RResponseTeamApplyMessage, &pbCross.S2R_ResponseTeamApplyMessage{Applicant: int32(player.GameId), LeaderId: int32(leaderId)}, 1)
	if net_helper.IsNodeNotFoundError(err) {
		// 消息来迟了 邀请者离线了
		return Response.E6001
	}
	return Response.NoError
}

/* logic-code-end */
