package gameMap

import (
	"world/base/cfg"
	"world/common/pbBase/Response"
	"world/common/pbGame"
	"world/game/gameBase/gameStruct"
	"world/game/gameMgr"

	"github.com/huyangv/vmqant/module"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// C2sJumpMapMessageHandler 跳转地图
func C2sJumpMapMessageHandler(this module.RPCModule) any {
	/* action-code-start */
	return func(player *gameStruct.Player, msg *pbGame.C2S_JumpMapMessage) protoreflect.ProtoMessage {
		mapId := int(msg.GetToMapId())
		x := int(msg.GetToMapX())
		y := int(msg.GetToMapY())
		_, exists := cfg.ContainerMap.GetBeanByUnique(mapId)
		if !exists {
			return &pbGame.S2C_JumpMapMessage{
				Code: Response.ErrMapClosed,
			}
		}
		if player.MapId == mapId {
			return &pbGame.S2C_JumpMapMessage{
				Code: Response.Unknown,
			}
		}
		gameMgr.Map().EnterMap(player, mapId, x, y)
		return &pbGame.S2C_JumpMapMessage{}
	}
	/* action-code-end */
}

/* logic-code-start */
/* logic-code-end */
