package node

import (
	"world/common/pbCross"
	"world/game/gameMgr"
	ut "world/utils"

	"github.com/huyangv/vmqant/module"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// S2rBroadcastTeamEnterMapMessageHandler 广播队伍切换地图
func S2rBroadcastTeamEnterMapMessageHandler(this module.RPCModule) any {
	/* action-code-start */
	return func(msg *pbCross.S2R_BroadcastTeamEnterMapMessage) protoreflect.ProtoMessage {

		units := gameMgr.Map().ProcessTeamEnterMap(int(msg.GetFromMapId()), int(msg.GetToMapId()), int(msg.GetToMapX()), int(msg.GetToMapY()), int(msg.GetLeader()), ut.ToInt(msg.GetMember()))
		return &pbCross.R2S_ReplayPlayerEnterMapMessage{Plr: units}
	}
	/* action-code-end */
}

/* logic-code-start */
/* logic-code-end */
