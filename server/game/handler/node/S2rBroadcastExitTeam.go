package node

import (
	"world/common/pbCross"
	"world/game/gameMgr"

	"github.com/huyangv/vmqant/module"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// S2rBroadcastExitTeamMessageHandler 广播退出队伍
func S2rBroadcastExitTeamMessageHandler(this module.RPCModule) any {
	/* action-code-start */
	return func(msg *pbCross.S2R_BroadcastExitTeamMessage) protoreflect.ProtoMessage {

		gameMgr.Team().OnSyncExitTeam(int(msg.MapId), int(msg.Leader), int(msg.Member))
		return nil
	}
	/* action-code-end */
}

/* logic-code-start */
/* logic-code-end */
