package node

import (
	"world/common/pbCross"

	"github.com/huyangv/vmqant/module"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// S2rBroadcastChangeLeaderMessageHandler 广播改变队长
func S2rBroadcastChangeLeaderMessageHandler(this module.RPCModule) any {
	/* action-code-start */
	return func(msg *pbCross.S2R_BroadcastChangeLeaderMessage) protoreflect.ProtoMessage {

		return nil
	}
	/* action-code-end */
}

/* logic-code-start */
/* logic-code-end */
