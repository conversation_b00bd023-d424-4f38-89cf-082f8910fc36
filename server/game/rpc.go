package game

import (
	"world/game/handler/bag"
	"world/game/handler/battle"
	"world/game/handler/common"
	"world/game/handler/gameMap"
	"world/game/handler/node"
	"world/game/handler/pet"
	"world/game/handler/role"
	"world/game/handler/task"
)

// InitRpc 自动生成，不要在这个方法添加任何内容。
func InitRpc(this *Game) {
	// 物品绑定
	this.middleware.Wrap("C2S_BagItemBindMessage", bag.C2sBagItemBindMessageHandler)
	// 宝石镶嵌
	this.middleware.Wrap("C2S_BagItemEnchaseMessage", bag.C2sBagItemEnchaseMessageHandler)
	// 宝石替换
	this.middleware.Wrap("C2S_BagItemGemReplaceMessage", bag.C2sBagItemGemReplaceMessageHandler)
	// 装备物品鉴定
	this.middleware.Wrap("C2S_BagItemIdentifyMessage", bag.C2sBagItemIdentifyMessageHandler)
	// 鉴定响应
	this.middleware.Wrap("C2S_BagItemIdentifyAnswerMessage", bag.C2sBagItemIdentifyAnswerMessageHandler)
	// 物品操作
	this.middleware.Wrap("C2S_BagItemSellMessage", bag.C2sBagItemSellMessageHandler)
	// 装备物品升星
	this.middleware.Wrap("C2S_BagItemStarMessage", bag.C2sBagItemStarMessageHandler)
	// 整理物品
	this.middleware.Wrap("C2S_BagResetMessage", bag.C2sBagResetMessageHandler)
	// 脱下装备
	this.middleware.Wrap("C2S_EquipTakeOffMessage", bag.C2sEquipTakeOffMessageHandler)
	// 穿戴装备
	this.middleware.Wrap("C2S_EquipWearMessage", bag.C2sEquipWearMessageHandler)
	// 使用物品
	this.middleware.Wrap("C2S_PlayerBagUseMessage", bag.C2sPlayerBagUseMessageHandler)
	// 战斗测试
	this.middleware.Wrap("C2S_BattlePassTestMessage", battle.C2sBattlePassTestMessageHandler)
	// 校验本地战斗
	this.middleware.Wrap("C2S_RunLocalBattleMessage", battle.C2sRunLocalBattleMessageHandler)
	// 创建角色
	this.middleware.Wrap("C2S_CreateRoleMessage", common.C2sCreateRoleMessageHandler)
	// 角色登入
	this.middleware.Wrap("C2S_EnterGameMessage", common.C2sEnterGameMessageHandler)
	// 拉取角色列表
	this.middleware.Wrap("C2S_GetPlayerListMessage", common.C2sGetPlayerListMessageHandler)
	// 执行gm
	this.middleware.Wrap("C2S_GmExecuteMessage", common.C2sGmExecuteMessageHandler)
	// 跳转地图
	this.middleware.Wrap("C2S_JumpMapMessage", gameMap.C2sJumpMapMessageHandler)
	// 玩家对于场景事件的选择
	this.middleware.Wrap("C2S_PlayerEventChooseMessage", gameMap.C2sPlayerEventChooseMessageHandler)
	// 玩家移动
	this.middleware.Wrap("C2S_PlayerMoveMessage", gameMap.C2sPlayerMoveMessageHandler)
	// 邀请加入队伍
	this.middleware.Wrap("C2S_TeamInviteMessage", gameMap.C2sTeamInviteMessageHandler)
	// 加入队伍
	this.middleware.Wrap("C2S_TeamJoinMessage", gameMap.C2sTeamJoinMessageHandler)
	// 广播加入队伍
	this.middleware.Wrap("S2R_BroadcastJoinTeamMessage", node.S2rBroadcastJoinTeamMessageHandler)
	// 将消息转发到玩家所在节点，然后通过玩家的session发送给客户端
	this.middleware.Wrap("S2R_ForwardMessage", node.S2rForwardMessageHandler)
	// 查询玩家是不是在节点中在线
	this.middleware.Wrap("S2R_IsPlayerOnlineMessage", node.S2rIsPlayerOnlineMessageHandler)
	// 节点之间相互通知，使玩家离线
	this.middleware.Wrap("S2R_KickPlayerForceByPidMessage", node.S2rKickPlayerForceByPidMessageHandler)
	// 节点之间相互通知，使玩家离线
	this.middleware.Wrap("S2R_KickPlayerForceByUidMessage", node.S2rKickPlayerForceByUidMessageHandler)
	// 通知，玩家进入某个地图
	this.middleware.Wrap("S2R_NotifyPlayerEnterMapMessage", node.S2rNotifyPlayerEnterMapMessageHandler)
	// 通知，玩家离开某个地图
	this.middleware.Wrap("S2R_NotifyPlayerLeaveMapMessage", node.S2rNotifyPlayerLeaveMapMessageHandler)
	// 通知，玩家移动
	this.middleware.Wrap("S2R_NotifyPlayerMoveMessage", node.S2rNotifyPlayerMoveMessageHandler)
	// 通知，玩家申请入队
	this.middleware.Wrap("S2R_NotifyTeamApplyMessage", node.S2rNotifyTeamApplyMessageHandler)
	// 通知，邀请玩家入队
	this.middleware.Wrap("S2R_NotifyTeamInviteMessage", node.S2rNotifyTeamInviteMessageHandler)
	// session离线
	this.middleware.Wrap("S2R_OnLeaveMessage", node.S2rOnLeaveMessageHandler)
	// 用户选区登录后通知逻辑服
	this.middleware.Wrap("S2R_OnPlayerLoginMessage", node.S2rOnPlayerLoginMessageHandler)
	// 最终响应入队申请
	this.middleware.Wrap("S2R_ResponseTeamApplyMessage", node.S2rResponseTeamApplyMessageHandler)
	// 最终响应入队邀请
	this.middleware.Wrap("S2R_ResponseTeamInviteMessage", node.S2rResponseTeamInviteMessageHandler)
	// 宠物潜能石结果替换确认
	this.middleware.Wrap("C2S_PetAddSkillSureMessage", pet.C2sPetAddSkillSureMessageHandler)
	// 宠物封印
	this.middleware.Wrap("C2S_PetSealMessage", pet.C2sPetSealMessageHandler)
	// 宠物使用技能书
	this.middleware.Wrap("C2S_PetSkillBookLearnMessage", pet.C2sPetSkillBookLearnMessageHandler)
	// 用户加点操作
	this.middleware.Wrap("C2S_ActorAttributeMessage", role.C2sActorAttributeMessageHandler)
	// 自动释放的技能设置
	this.middleware.Wrap("C2S_AutoSkillSetMessage", role.C2sAutoSkillSetMessageHandler)
	// 学习技能
	this.middleware.Wrap("C2S_LearnSkillByShopMessage", role.C2sLearnSkillByShopMessageHandler)
	// 领取任务
	this.middleware.Wrap("C2S_AcceptTaskMessage", task.C2sAcceptTaskMessageHandler)
	// 放弃任务
	this.middleware.Wrap("C2S_DeleteTaskMessage", task.C2sDeleteTaskMessageHandler)
	// 提交任务
	this.middleware.Wrap("C2S_SubmitTaskMessage", task.C2sSubmitTaskMessageHandler)
}
