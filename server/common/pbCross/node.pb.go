// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v6.30.1
// source: pbCross/node.proto

package pbCross

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	Response "world/common/pbBase/Response"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// After are messages.
type S2R_OnLeaveMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *S2R_OnLeaveMessage) Reset() {
	*x = S2R_OnLeaveMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbCross_node_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2R_OnLeaveMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2R_OnLeaveMessage) ProtoMessage() {}

func (x *S2R_OnLeaveMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbCross_node_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2R_OnLeaveMessage.ProtoReflect.Descriptor instead.
func (*S2R_OnLeaveMessage) Descriptor() ([]byte, []int) {
	return file_pbCross_node_proto_rawDescGZIP(), []int{0}
}

type S2R_OnPlayerLoginMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *S2R_OnPlayerLoginMessage) Reset() {
	*x = S2R_OnPlayerLoginMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbCross_node_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2R_OnPlayerLoginMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2R_OnPlayerLoginMessage) ProtoMessage() {}

func (x *S2R_OnPlayerLoginMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbCross_node_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2R_OnPlayerLoginMessage.ProtoReflect.Descriptor instead.
func (*S2R_OnPlayerLoginMessage) Descriptor() ([]byte, []int) {
	return file_pbCross_node_proto_rawDescGZIP(), []int{1}
}

type R2S_OnPlayerLoginMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Err string `protobuf:"bytes,1,opt,name=err,proto3" json:"err,omitempty"` //错误字符串
}

func (x *R2S_OnPlayerLoginMessage) Reset() {
	*x = R2S_OnPlayerLoginMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbCross_node_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *R2S_OnPlayerLoginMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*R2S_OnPlayerLoginMessage) ProtoMessage() {}

func (x *R2S_OnPlayerLoginMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbCross_node_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use R2S_OnPlayerLoginMessage.ProtoReflect.Descriptor instead.
func (*R2S_OnPlayerLoginMessage) Descriptor() ([]byte, []int) {
	return file_pbCross_node_proto_rawDescGZIP(), []int{2}
}

func (x *R2S_OnPlayerLoginMessage) GetErr() string {
	if x != nil {
		return x.Err
	}
	return ""
}

type S2R_IsPlayerOnlineMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"` //玩家uid
}

func (x *S2R_IsPlayerOnlineMessage) Reset() {
	*x = S2R_IsPlayerOnlineMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbCross_node_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2R_IsPlayerOnlineMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2R_IsPlayerOnlineMessage) ProtoMessage() {}

func (x *S2R_IsPlayerOnlineMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbCross_node_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2R_IsPlayerOnlineMessage.ProtoReflect.Descriptor instead.
func (*S2R_IsPlayerOnlineMessage) Descriptor() ([]byte, []int) {
	return file_pbCross_node_proto_rawDescGZIP(), []int{3}
}

func (x *S2R_IsPlayerOnlineMessage) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

type R2S_IsPlayerOnlineMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Is bool `protobuf:"varint,1,opt,name=is,proto3" json:"is,omitempty"` //是否在线
}

func (x *R2S_IsPlayerOnlineMessage) Reset() {
	*x = R2S_IsPlayerOnlineMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbCross_node_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *R2S_IsPlayerOnlineMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*R2S_IsPlayerOnlineMessage) ProtoMessage() {}

func (x *R2S_IsPlayerOnlineMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbCross_node_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use R2S_IsPlayerOnlineMessage.ProtoReflect.Descriptor instead.
func (*R2S_IsPlayerOnlineMessage) Descriptor() ([]byte, []int) {
	return file_pbCross_node_proto_rawDescGZIP(), []int{4}
}

func (x *R2S_IsPlayerOnlineMessage) GetIs() bool {
	if x != nil {
		return x.Is
	}
	return false
}

type S2R_KickPlayerForceByPidMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pid string `protobuf:"bytes,1,opt,name=pid,proto3" json:"pid,omitempty"` //玩家pid
}

func (x *S2R_KickPlayerForceByPidMessage) Reset() {
	*x = S2R_KickPlayerForceByPidMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbCross_node_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2R_KickPlayerForceByPidMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2R_KickPlayerForceByPidMessage) ProtoMessage() {}

func (x *S2R_KickPlayerForceByPidMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbCross_node_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2R_KickPlayerForceByPidMessage.ProtoReflect.Descriptor instead.
func (*S2R_KickPlayerForceByPidMessage) Descriptor() ([]byte, []int) {
	return file_pbCross_node_proto_rawDescGZIP(), []int{5}
}

func (x *S2R_KickPlayerForceByPidMessage) GetPid() string {
	if x != nil {
		return x.Pid
	}
	return ""
}

type S2R_KickPlayerForceByUidMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"` //玩家所属用户uid
}

func (x *S2R_KickPlayerForceByUidMessage) Reset() {
	*x = S2R_KickPlayerForceByUidMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbCross_node_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2R_KickPlayerForceByUidMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2R_KickPlayerForceByUidMessage) ProtoMessage() {}

func (x *S2R_KickPlayerForceByUidMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbCross_node_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2R_KickPlayerForceByUidMessage.ProtoReflect.Descriptor instead.
func (*S2R_KickPlayerForceByUidMessage) Descriptor() ([]byte, []int) {
	return file_pbCross_node_proto_rawDescGZIP(), []int{6}
}

func (x *S2R_KickPlayerForceByUidMessage) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

type S2R_KickSessionByUidMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"` //玩家所属用户uid
}

func (x *S2R_KickSessionByUidMessage) Reset() {
	*x = S2R_KickSessionByUidMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbCross_node_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2R_KickSessionByUidMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2R_KickSessionByUidMessage) ProtoMessage() {}

func (x *S2R_KickSessionByUidMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbCross_node_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2R_KickSessionByUidMessage.ProtoReflect.Descriptor instead.
func (*S2R_KickSessionByUidMessage) Descriptor() ([]byte, []int) {
	return file_pbCross_node_proto_rawDescGZIP(), []int{7}
}

func (x *S2R_KickSessionByUidMessage) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

type S2R_RemoveUserByUidMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"` //用户uid
}

func (x *S2R_RemoveUserByUidMessage) Reset() {
	*x = S2R_RemoveUserByUidMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbCross_node_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2R_RemoveUserByUidMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2R_RemoveUserByUidMessage) ProtoMessage() {}

func (x *S2R_RemoveUserByUidMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbCross_node_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2R_RemoveUserByUidMessage.ProtoReflect.Descriptor instead.
func (*S2R_RemoveUserByUidMessage) Descriptor() ([]byte, []int) {
	return file_pbCross_node_proto_rawDescGZIP(), []int{8}
}

func (x *S2R_RemoveUserByUidMessage) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

type R2S_KickPlayerForceMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Is bool `protobuf:"varint,1,opt,name=is,proto3" json:"is,omitempty"` //玩家在当前节点，并且使其离线，则返回true
}

func (x *R2S_KickPlayerForceMessage) Reset() {
	*x = R2S_KickPlayerForceMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbCross_node_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *R2S_KickPlayerForceMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*R2S_KickPlayerForceMessage) ProtoMessage() {}

func (x *R2S_KickPlayerForceMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbCross_node_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use R2S_KickPlayerForceMessage.ProtoReflect.Descriptor instead.
func (*R2S_KickPlayerForceMessage) Descriptor() ([]byte, []int) {
	return file_pbCross_node_proto_rawDescGZIP(), []int{9}
}

func (x *R2S_KickPlayerForceMessage) GetIs() bool {
	if x != nil {
		return x.Is
	}
	return false
}

type R2S_SimpleResponseMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code Response.Code `protobuf:"varint,1,opt,name=code,proto3,enum=proto.Response.Code" json:"code,omitempty"` //响应码
	Data string        `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`                           //数据
}

func (x *R2S_SimpleResponseMessage) Reset() {
	*x = R2S_SimpleResponseMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbCross_node_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *R2S_SimpleResponseMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*R2S_SimpleResponseMessage) ProtoMessage() {}

func (x *R2S_SimpleResponseMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbCross_node_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use R2S_SimpleResponseMessage.ProtoReflect.Descriptor instead.
func (*R2S_SimpleResponseMessage) Descriptor() ([]byte, []int) {
	return file_pbCross_node_proto_rawDescGZIP(), []int{10}
}

func (x *R2S_SimpleResponseMessage) GetCode() Response.Code {
	if x != nil {
		return x.Code
	}
	return Response.Code(0)
}

func (x *R2S_SimpleResponseMessage) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

type S2R_NotifyPlayerEnterMapMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FromMapId int32                `protobuf:"varint,1,opt,name=fromMapId,proto3" json:"fromMapId,omitempty"` //之前的地图id
	ToMapId   int32                `protobuf:"varint,2,opt,name=toMapId,proto3" json:"toMapId,omitempty"`     //要进入的地图id
	ToMapX    int32                `protobuf:"varint,3,opt,name=toMapX,proto3" json:"toMapX,omitempty"`       //要进入的地图x
	ToMapY    int32                `protobuf:"varint,4,opt,name=toMapY,proto3" json:"toMapY,omitempty"`       //要进入的地图y
	Plr       []*CrossSimplePlayer `protobuf:"bytes,5,rep,name=plr,proto3" json:"plr,omitempty"`              //玩家简要数据
}

func (x *S2R_NotifyPlayerEnterMapMessage) Reset() {
	*x = S2R_NotifyPlayerEnterMapMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbCross_node_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2R_NotifyPlayerEnterMapMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2R_NotifyPlayerEnterMapMessage) ProtoMessage() {}

func (x *S2R_NotifyPlayerEnterMapMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbCross_node_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2R_NotifyPlayerEnterMapMessage.ProtoReflect.Descriptor instead.
func (*S2R_NotifyPlayerEnterMapMessage) Descriptor() ([]byte, []int) {
	return file_pbCross_node_proto_rawDescGZIP(), []int{11}
}

func (x *S2R_NotifyPlayerEnterMapMessage) GetFromMapId() int32 {
	if x != nil {
		return x.FromMapId
	}
	return 0
}

func (x *S2R_NotifyPlayerEnterMapMessage) GetToMapId() int32 {
	if x != nil {
		return x.ToMapId
	}
	return 0
}

func (x *S2R_NotifyPlayerEnterMapMessage) GetToMapX() int32 {
	if x != nil {
		return x.ToMapX
	}
	return 0
}

func (x *S2R_NotifyPlayerEnterMapMessage) GetToMapY() int32 {
	if x != nil {
		return x.ToMapY
	}
	return 0
}

func (x *S2R_NotifyPlayerEnterMapMessage) GetPlr() []*CrossSimplePlayer {
	if x != nil {
		return x.Plr
	}
	return nil
}

type R2S_ReplayPlayerEnterMapMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Plr []*CrossSimplePlayer `protobuf:"bytes,1,rep,name=plr,proto3" json:"plr,omitempty"` //玩家简要数据
}

func (x *R2S_ReplayPlayerEnterMapMessage) Reset() {
	*x = R2S_ReplayPlayerEnterMapMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbCross_node_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *R2S_ReplayPlayerEnterMapMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*R2S_ReplayPlayerEnterMapMessage) ProtoMessage() {}

func (x *R2S_ReplayPlayerEnterMapMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbCross_node_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use R2S_ReplayPlayerEnterMapMessage.ProtoReflect.Descriptor instead.
func (*R2S_ReplayPlayerEnterMapMessage) Descriptor() ([]byte, []int) {
	return file_pbCross_node_proto_rawDescGZIP(), []int{12}
}

func (x *R2S_ReplayPlayerEnterMapMessage) GetPlr() []*CrossSimplePlayer {
	if x != nil {
		return x.Plr
	}
	return nil
}

type S2R_NotifyPlayerLeaveMapMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MapId int32 `protobuf:"varint,1,opt,name=mapId,proto3" json:"mapId,omitempty"` //地图id
	Id    int32 `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`       //玩家id
}

func (x *S2R_NotifyPlayerLeaveMapMessage) Reset() {
	*x = S2R_NotifyPlayerLeaveMapMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbCross_node_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2R_NotifyPlayerLeaveMapMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2R_NotifyPlayerLeaveMapMessage) ProtoMessage() {}

func (x *S2R_NotifyPlayerLeaveMapMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbCross_node_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2R_NotifyPlayerLeaveMapMessage.ProtoReflect.Descriptor instead.
func (*S2R_NotifyPlayerLeaveMapMessage) Descriptor() ([]byte, []int) {
	return file_pbCross_node_proto_rawDescGZIP(), []int{13}
}

func (x *S2R_NotifyPlayerLeaveMapMessage) GetMapId() int32 {
	if x != nil {
		return x.MapId
	}
	return 0
}

func (x *S2R_NotifyPlayerLeaveMapMessage) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

type S2R_NotifyPlayerMoveMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MapId int32 `protobuf:"varint,1,opt,name=mapId,proto3" json:"mapId,omitempty"` //地图id
	Id    int32 `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`       //玩家id
	X     int32 `protobuf:"varint,3,opt,name=x,proto3" json:"x,omitempty"`         //x
	Y     int32 `protobuf:"varint,4,opt,name=y,proto3" json:"y,omitempty"`         //y
	Mode  int32 `protobuf:"varint,5,opt,name=mode,proto3" json:"mode,omitempty"`   //mode
}

func (x *S2R_NotifyPlayerMoveMessage) Reset() {
	*x = S2R_NotifyPlayerMoveMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbCross_node_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2R_NotifyPlayerMoveMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2R_NotifyPlayerMoveMessage) ProtoMessage() {}

func (x *S2R_NotifyPlayerMoveMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbCross_node_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2R_NotifyPlayerMoveMessage.ProtoReflect.Descriptor instead.
func (*S2R_NotifyPlayerMoveMessage) Descriptor() ([]byte, []int) {
	return file_pbCross_node_proto_rawDescGZIP(), []int{14}
}

func (x *S2R_NotifyPlayerMoveMessage) GetMapId() int32 {
	if x != nil {
		return x.MapId
	}
	return 0
}

func (x *S2R_NotifyPlayerMoveMessage) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *S2R_NotifyPlayerMoveMessage) GetX() int32 {
	if x != nil {
		return x.X
	}
	return 0
}

func (x *S2R_NotifyPlayerMoveMessage) GetY() int32 {
	if x != nil {
		return x.Y
	}
	return 0
}

func (x *S2R_NotifyPlayerMoveMessage) GetMode() int32 {
	if x != nil {
		return x.Mode
	}
	return 0
}

type S2R_ForwardMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`          //玩家id
	GameId int32  `protobuf:"varint,2,opt,name=gameId,proto3" json:"gameId,omitempty"` //玩家gameId
	Router string `protobuf:"bytes,3,opt,name=router,proto3" json:"router,omitempty"`  //路由
	Msg    []byte `protobuf:"bytes,4,opt,name=msg,proto3" json:"msg,omitempty"`        //消息
}

func (x *S2R_ForwardMessage) Reset() {
	*x = S2R_ForwardMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbCross_node_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2R_ForwardMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2R_ForwardMessage) ProtoMessage() {}

func (x *S2R_ForwardMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbCross_node_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2R_ForwardMessage.ProtoReflect.Descriptor instead.
func (*S2R_ForwardMessage) Descriptor() ([]byte, []int) {
	return file_pbCross_node_proto_rawDescGZIP(), []int{15}
}

func (x *S2R_ForwardMessage) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *S2R_ForwardMessage) GetGameId() int32 {
	if x != nil {
		return x.GameId
	}
	return 0
}

func (x *S2R_ForwardMessage) GetRouter() string {
	if x != nil {
		return x.Router
	}
	return ""
}

func (x *S2R_ForwardMessage) GetMsg() []byte {
	if x != nil {
		return x.Msg
	}
	return nil
}

type S2R_NotifyTeamInviteMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       int32              `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`             //邀请的玩家id
	LeaderId int32              `protobuf:"varint,2,opt,name=leaderId,proto3" json:"leaderId,omitempty"` //队长id
	Inviter  *CrossSimplePlayer `protobuf:"bytes,3,opt,name=inviter,proto3" json:"inviter,omitempty"`    //邀请者
}

func (x *S2R_NotifyTeamInviteMessage) Reset() {
	*x = S2R_NotifyTeamInviteMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbCross_node_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2R_NotifyTeamInviteMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2R_NotifyTeamInviteMessage) ProtoMessage() {}

func (x *S2R_NotifyTeamInviteMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbCross_node_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2R_NotifyTeamInviteMessage.ProtoReflect.Descriptor instead.
func (*S2R_NotifyTeamInviteMessage) Descriptor() ([]byte, []int) {
	return file_pbCross_node_proto_rawDescGZIP(), []int{16}
}

func (x *S2R_NotifyTeamInviteMessage) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *S2R_NotifyTeamInviteMessage) GetLeaderId() int32 {
	if x != nil {
		return x.LeaderId
	}
	return 0
}

func (x *S2R_NotifyTeamInviteMessage) GetInviter() *CrossSimplePlayer {
	if x != nil {
		return x.Inviter
	}
	return nil
}

type S2R_ResponseTeamInviteMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	To       int32 `protobuf:"varint,1,opt,name=to,proto3" json:"to,omitempty"`             //响应者
	Inviter  int32 `protobuf:"varint,2,opt,name=inviter,proto3" json:"inviter,omitempty"`   //邀请者
	LeaderId int32 `protobuf:"varint,3,opt,name=leaderId,proto3" json:"leaderId,omitempty"` //队长id
}

func (x *S2R_ResponseTeamInviteMessage) Reset() {
	*x = S2R_ResponseTeamInviteMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbCross_node_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2R_ResponseTeamInviteMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2R_ResponseTeamInviteMessage) ProtoMessage() {}

func (x *S2R_ResponseTeamInviteMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbCross_node_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2R_ResponseTeamInviteMessage.ProtoReflect.Descriptor instead.
func (*S2R_ResponseTeamInviteMessage) Descriptor() ([]byte, []int) {
	return file_pbCross_node_proto_rawDescGZIP(), []int{17}
}

func (x *S2R_ResponseTeamInviteMessage) GetTo() int32 {
	if x != nil {
		return x.To
	}
	return 0
}

func (x *S2R_ResponseTeamInviteMessage) GetInviter() int32 {
	if x != nil {
		return x.Inviter
	}
	return 0
}

func (x *S2R_ResponseTeamInviteMessage) GetLeaderId() int32 {
	if x != nil {
		return x.LeaderId
	}
	return 0
}

type S2R_BroadcastJoinTeamMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MapId      int32 `protobuf:"varint,1,opt,name=mapId,proto3" json:"mapId,omitempty"`           //在哪个地图
	Leader     int32 `protobuf:"varint,2,opt,name=leader,proto3" json:"leader,omitempty"`         //队长id
	Member     int32 `protobuf:"varint,3,opt,name=member,proto3" json:"member,omitempty"`         //队员id
	LockMember bool  `protobuf:"varint,4,opt,name=lockMember,proto3" json:"lockMember,omitempty"` //是否需要锁住队员
}

func (x *S2R_BroadcastJoinTeamMessage) Reset() {
	*x = S2R_BroadcastJoinTeamMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbCross_node_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2R_BroadcastJoinTeamMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2R_BroadcastJoinTeamMessage) ProtoMessage() {}

func (x *S2R_BroadcastJoinTeamMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbCross_node_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2R_BroadcastJoinTeamMessage.ProtoReflect.Descriptor instead.
func (*S2R_BroadcastJoinTeamMessage) Descriptor() ([]byte, []int) {
	return file_pbCross_node_proto_rawDescGZIP(), []int{18}
}

func (x *S2R_BroadcastJoinTeamMessage) GetMapId() int32 {
	if x != nil {
		return x.MapId
	}
	return 0
}

func (x *S2R_BroadcastJoinTeamMessage) GetLeader() int32 {
	if x != nil {
		return x.Leader
	}
	return 0
}

func (x *S2R_BroadcastJoinTeamMessage) GetMember() int32 {
	if x != nil {
		return x.Member
	}
	return 0
}

func (x *S2R_BroadcastJoinTeamMessage) GetLockMember() bool {
	if x != nil {
		return x.LockMember
	}
	return false
}

type S2R_NotifyTeamApplyMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LeaderId  int32              `protobuf:"varint,1,opt,name=leaderId,proto3" json:"leaderId,omitempty"`  //队长id
	Applicant *CrossSimplePlayer `protobuf:"bytes,2,opt,name=applicant,proto3" json:"applicant,omitempty"` //申请人
}

func (x *S2R_NotifyTeamApplyMessage) Reset() {
	*x = S2R_NotifyTeamApplyMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbCross_node_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2R_NotifyTeamApplyMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2R_NotifyTeamApplyMessage) ProtoMessage() {}

func (x *S2R_NotifyTeamApplyMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbCross_node_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2R_NotifyTeamApplyMessage.ProtoReflect.Descriptor instead.
func (*S2R_NotifyTeamApplyMessage) Descriptor() ([]byte, []int) {
	return file_pbCross_node_proto_rawDescGZIP(), []int{19}
}

func (x *S2R_NotifyTeamApplyMessage) GetLeaderId() int32 {
	if x != nil {
		return x.LeaderId
	}
	return 0
}

func (x *S2R_NotifyTeamApplyMessage) GetApplicant() *CrossSimplePlayer {
	if x != nil {
		return x.Applicant
	}
	return nil
}

type S2R_ResponseTeamApplyMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LeaderId  int32 `protobuf:"varint,1,opt,name=leaderId,proto3" json:"leaderId,omitempty"`   //队长id
	Applicant int32 `protobuf:"varint,2,opt,name=applicant,proto3" json:"applicant,omitempty"` //申请者
}

func (x *S2R_ResponseTeamApplyMessage) Reset() {
	*x = S2R_ResponseTeamApplyMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbCross_node_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2R_ResponseTeamApplyMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2R_ResponseTeamApplyMessage) ProtoMessage() {}

func (x *S2R_ResponseTeamApplyMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbCross_node_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2R_ResponseTeamApplyMessage.ProtoReflect.Descriptor instead.
func (*S2R_ResponseTeamApplyMessage) Descriptor() ([]byte, []int) {
	return file_pbCross_node_proto_rawDescGZIP(), []int{20}
}

func (x *S2R_ResponseTeamApplyMessage) GetLeaderId() int32 {
	if x != nil {
		return x.LeaderId
	}
	return 0
}

func (x *S2R_ResponseTeamApplyMessage) GetApplicant() int32 {
	if x != nil {
		return x.Applicant
	}
	return 0
}

type S2R_NotifyExitTeamMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MapId  int32 `protobuf:"varint,1,opt,name=mapId,proto3" json:"mapId,omitempty"`   //在哪个地图
	Leader int32 `protobuf:"varint,2,opt,name=leader,proto3" json:"leader,omitempty"` //队长id
	Member int32 `protobuf:"varint,3,opt,name=member,proto3" json:"member,omitempty"` //队员id
}

func (x *S2R_NotifyExitTeamMessage) Reset() {
	*x = S2R_NotifyExitTeamMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbCross_node_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2R_NotifyExitTeamMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2R_NotifyExitTeamMessage) ProtoMessage() {}

func (x *S2R_NotifyExitTeamMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbCross_node_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2R_NotifyExitTeamMessage.ProtoReflect.Descriptor instead.
func (*S2R_NotifyExitTeamMessage) Descriptor() ([]byte, []int) {
	return file_pbCross_node_proto_rawDescGZIP(), []int{21}
}

func (x *S2R_NotifyExitTeamMessage) GetMapId() int32 {
	if x != nil {
		return x.MapId
	}
	return 0
}

func (x *S2R_NotifyExitTeamMessage) GetLeader() int32 {
	if x != nil {
		return x.Leader
	}
	return 0
}

func (x *S2R_NotifyExitTeamMessage) GetMember() int32 {
	if x != nil {
		return x.Member
	}
	return 0
}

type S2R_BroadcastExitTeamMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MapId      int32 `protobuf:"varint,1,opt,name=mapId,proto3" json:"mapId,omitempty"`           //在哪个地图
	Leader     int32 `protobuf:"varint,2,opt,name=leader,proto3" json:"leader,omitempty"`         //队长id
	Member     int32 `protobuf:"varint,3,opt,name=member,proto3" json:"member,omitempty"`         //队员id
	LockMember bool  `protobuf:"varint,4,opt,name=lockMember,proto3" json:"lockMember,omitempty"` //是否需要锁住队员
}

func (x *S2R_BroadcastExitTeamMessage) Reset() {
	*x = S2R_BroadcastExitTeamMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbCross_node_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2R_BroadcastExitTeamMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2R_BroadcastExitTeamMessage) ProtoMessage() {}

func (x *S2R_BroadcastExitTeamMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbCross_node_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2R_BroadcastExitTeamMessage.ProtoReflect.Descriptor instead.
func (*S2R_BroadcastExitTeamMessage) Descriptor() ([]byte, []int) {
	return file_pbCross_node_proto_rawDescGZIP(), []int{22}
}

func (x *S2R_BroadcastExitTeamMessage) GetMapId() int32 {
	if x != nil {
		return x.MapId
	}
	return 0
}

func (x *S2R_BroadcastExitTeamMessage) GetLeader() int32 {
	if x != nil {
		return x.Leader
	}
	return 0
}

func (x *S2R_BroadcastExitTeamMessage) GetMember() int32 {
	if x != nil {
		return x.Member
	}
	return 0
}

func (x *S2R_BroadcastExitTeamMessage) GetLockMember() bool {
	if x != nil {
		return x.LockMember
	}
	return false
}

type S2R_BroadcastChangeLeaderMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MapId  int32 `protobuf:"varint,1,opt,name=mapId,proto3" json:"mapId,omitempty"`   //在哪个地图
	Leader int32 `protobuf:"varint,2,opt,name=leader,proto3" json:"leader,omitempty"` //原队长id
	Member int32 `protobuf:"varint,3,opt,name=member,proto3" json:"member,omitempty"` //即将成为队长的id
}

func (x *S2R_BroadcastChangeLeaderMessage) Reset() {
	*x = S2R_BroadcastChangeLeaderMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbCross_node_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2R_BroadcastChangeLeaderMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2R_BroadcastChangeLeaderMessage) ProtoMessage() {}

func (x *S2R_BroadcastChangeLeaderMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbCross_node_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2R_BroadcastChangeLeaderMessage.ProtoReflect.Descriptor instead.
func (*S2R_BroadcastChangeLeaderMessage) Descriptor() ([]byte, []int) {
	return file_pbCross_node_proto_rawDescGZIP(), []int{23}
}

func (x *S2R_BroadcastChangeLeaderMessage) GetMapId() int32 {
	if x != nil {
		return x.MapId
	}
	return 0
}

func (x *S2R_BroadcastChangeLeaderMessage) GetLeader() int32 {
	if x != nil {
		return x.Leader
	}
	return 0
}

func (x *S2R_BroadcastChangeLeaderMessage) GetMember() int32 {
	if x != nil {
		return x.Member
	}
	return 0
}

type S2R_BroadcastDisbandTeamMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MapId  int32 `protobuf:"varint,1,opt,name=mapId,proto3" json:"mapId,omitempty"`   //在哪个地图
	Leader int32 `protobuf:"varint,2,opt,name=leader,proto3" json:"leader,omitempty"` //原队长id
}

func (x *S2R_BroadcastDisbandTeamMessage) Reset() {
	*x = S2R_BroadcastDisbandTeamMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbCross_node_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2R_BroadcastDisbandTeamMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2R_BroadcastDisbandTeamMessage) ProtoMessage() {}

func (x *S2R_BroadcastDisbandTeamMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbCross_node_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2R_BroadcastDisbandTeamMessage.ProtoReflect.Descriptor instead.
func (*S2R_BroadcastDisbandTeamMessage) Descriptor() ([]byte, []int) {
	return file_pbCross_node_proto_rawDescGZIP(), []int{24}
}

func (x *S2R_BroadcastDisbandTeamMessage) GetMapId() int32 {
	if x != nil {
		return x.MapId
	}
	return 0
}

func (x *S2R_BroadcastDisbandTeamMessage) GetLeader() int32 {
	if x != nil {
		return x.Leader
	}
	return 0
}

type S2R_BroadcastTeamEnterMapMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FromMapId int32   `protobuf:"varint,1,opt,name=fromMapId,proto3" json:"fromMapId,omitempty"`  //原地图id
	ToMapId   int32   `protobuf:"varint,2,opt,name=toMapId,proto3" json:"toMapId,omitempty"`      //目标地图id
	ToMapX    int32   `protobuf:"varint,3,opt,name=toMapX,proto3" json:"toMapX,omitempty"`        //目标地图x
	ToMapY    int32   `protobuf:"varint,4,opt,name=toMapY,proto3" json:"toMapY,omitempty"`        //目标地图y
	Leader    int32   `protobuf:"varint,5,opt,name=leader,proto3" json:"leader,omitempty"`        //队长id
	Member    []int32 `protobuf:"varint,6,rep,packed,name=member,proto3" json:"member,omitempty"` //队员id列表
}

func (x *S2R_BroadcastTeamEnterMapMessage) Reset() {
	*x = S2R_BroadcastTeamEnterMapMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbCross_node_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2R_BroadcastTeamEnterMapMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2R_BroadcastTeamEnterMapMessage) ProtoMessage() {}

func (x *S2R_BroadcastTeamEnterMapMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbCross_node_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2R_BroadcastTeamEnterMapMessage.ProtoReflect.Descriptor instead.
func (*S2R_BroadcastTeamEnterMapMessage) Descriptor() ([]byte, []int) {
	return file_pbCross_node_proto_rawDescGZIP(), []int{25}
}

func (x *S2R_BroadcastTeamEnterMapMessage) GetFromMapId() int32 {
	if x != nil {
		return x.FromMapId
	}
	return 0
}

func (x *S2R_BroadcastTeamEnterMapMessage) GetToMapId() int32 {
	if x != nil {
		return x.ToMapId
	}
	return 0
}

func (x *S2R_BroadcastTeamEnterMapMessage) GetToMapX() int32 {
	if x != nil {
		return x.ToMapX
	}
	return 0
}

func (x *S2R_BroadcastTeamEnterMapMessage) GetToMapY() int32 {
	if x != nil {
		return x.ToMapY
	}
	return 0
}

func (x *S2R_BroadcastTeamEnterMapMessage) GetLeader() int32 {
	if x != nil {
		return x.Leader
	}
	return 0
}

func (x *S2R_BroadcastTeamEnterMapMessage) GetMember() []int32 {
	if x != nil {
		return x.Member
	}
	return nil
}

var File_pbCross_node_proto protoreflect.FileDescriptor

var file_pbCross_node_proto_rawDesc = []byte{
	0x0a, 0x12, 0x70, 0x62, 0x43, 0x72, 0x6f, 0x73, 0x73, 0x2f, 0x6e, 0x6f, 0x64, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x70, 0x62, 0x42,
	0x61, 0x73, 0x65, 0x2f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2f, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x70, 0x62, 0x43,
	0x72, 0x6f, 0x73, 0x73, 0x2f, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0x14, 0x0a, 0x12, 0x53, 0x32, 0x52, 0x5f, 0x4f, 0x6e, 0x4c, 0x65, 0x61, 0x76, 0x65,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x1a, 0x0a, 0x18, 0x53, 0x32, 0x52, 0x5f, 0x4f,
	0x6e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x22, 0x2c, 0x0a, 0x18, 0x52, 0x32, 0x53, 0x5f, 0x4f, 0x6e, 0x50, 0x6c, 0x61,
	0x79, 0x65, 0x72, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12,
	0x10, 0x0a, 0x03, 0x65, 0x72, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x65, 0x72,
	0x72, 0x22, 0x2d, 0x0a, 0x19, 0x53, 0x32, 0x52, 0x5f, 0x49, 0x73, 0x50, 0x6c, 0x61, 0x79, 0x65,
	0x72, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x10,
	0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64,
	0x22, 0x2b, 0x0a, 0x19, 0x52, 0x32, 0x53, 0x5f, 0x49, 0x73, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72,
	0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x02, 0x69, 0x73, 0x22, 0x33, 0x0a,
	0x1f, 0x53, 0x32, 0x52, 0x5f, 0x4b, 0x69, 0x63, 0x6b, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x46,
	0x6f, 0x72, 0x63, 0x65, 0x42, 0x79, 0x50, 0x69, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x12, 0x10, 0x0a, 0x03, 0x70, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x70,
	0x69, 0x64, 0x22, 0x33, 0x0a, 0x1f, 0x53, 0x32, 0x52, 0x5f, 0x4b, 0x69, 0x63, 0x6b, 0x50, 0x6c,
	0x61, 0x79, 0x65, 0x72, 0x46, 0x6f, 0x72, 0x63, 0x65, 0x42, 0x79, 0x55, 0x69, 0x64, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x22, 0x2f, 0x0a, 0x1b, 0x53, 0x32, 0x52, 0x5f, 0x4b,
	0x69, 0x63, 0x6b, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x42, 0x79, 0x55, 0x69, 0x64, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x22, 0x2e, 0x0a, 0x1a, 0x53, 0x32, 0x52, 0x5f,
	0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x55, 0x73, 0x65, 0x72, 0x42, 0x79, 0x55, 0x69, 0x64, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x22, 0x2c, 0x0a, 0x1a, 0x52, 0x32, 0x53, 0x5f,
	0x4b, 0x69, 0x63, 0x6b, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x46, 0x6f, 0x72, 0x63, 0x65, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x02, 0x69, 0x73, 0x22, 0x59, 0x0a, 0x19, 0x52, 0x32, 0x53, 0x5f, 0x53, 0x69,
	0x6d, 0x70, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x2e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x22, 0xb5, 0x01, 0x0a, 0x1f, 0x53, 0x32, 0x52, 0x5f, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79,
	0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x4d, 0x61, 0x70, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x66, 0x72, 0x6f, 0x6d, 0x4d, 0x61, 0x70,
	0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x66, 0x72, 0x6f, 0x6d, 0x4d, 0x61,
	0x70, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x74, 0x6f, 0x4d, 0x61, 0x70, 0x49, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x74, 0x6f, 0x4d, 0x61, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a,
	0x06, 0x74, 0x6f, 0x4d, 0x61, 0x70, 0x58, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x74,
	0x6f, 0x4d, 0x61, 0x70, 0x58, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x6f, 0x4d, 0x61, 0x70, 0x59, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x74, 0x6f, 0x4d, 0x61, 0x70, 0x59, 0x12, 0x2a, 0x0a,
	0x03, 0x70, 0x6c, 0x72, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x43, 0x72, 0x6f, 0x73, 0x73, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x50, 0x6c,
	0x61, 0x79, 0x65, 0x72, 0x52, 0x03, 0x70, 0x6c, 0x72, 0x22, 0x4d, 0x0a, 0x1f, 0x52, 0x32, 0x53,
	0x5f, 0x52, 0x65, 0x70, 0x6c, 0x61, 0x79, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x45, 0x6e, 0x74,
	0x65, 0x72, 0x4d, 0x61, 0x70, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x2a, 0x0a, 0x03,
	0x70, 0x6c, 0x72, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x43, 0x72, 0x6f, 0x73, 0x73, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x50, 0x6c, 0x61,
	0x79, 0x65, 0x72, 0x52, 0x03, 0x70, 0x6c, 0x72, 0x22, 0x47, 0x0a, 0x1f, 0x53, 0x32, 0x52, 0x5f,
	0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x4c, 0x65, 0x61, 0x76,
	0x65, 0x4d, 0x61, 0x70, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6d,
	0x61, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6d, 0x61, 0x70, 0x49,
	0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69,
	0x64, 0x22, 0x73, 0x0a, 0x1b, 0x53, 0x32, 0x52, 0x5f, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x50,
	0x6c, 0x61, 0x79, 0x65, 0x72, 0x4d, 0x6f, 0x76, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x12, 0x14, 0x0a, 0x05, 0x6d, 0x61, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x05, 0x6d, 0x61, 0x70, 0x49, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x0c, 0x0a, 0x01, 0x78, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x01, 0x78, 0x12, 0x0c, 0x0a, 0x01, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x01, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x6d, 0x6f, 0x64, 0x65, 0x22, 0x66, 0x0a, 0x12, 0x53, 0x32, 0x52, 0x5f, 0x46, 0x6f,
	0x72, 0x77, 0x61, 0x72, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06,
	0x67, 0x61, 0x6d, 0x65, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x67, 0x61,
	0x6d, 0x65, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x72, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x72, 0x12, 0x10, 0x0a, 0x03,
	0x6d, 0x73, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x22, 0x7d,
	0x0a, 0x1b, 0x53, 0x32, 0x52, 0x5f, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x54, 0x65, 0x61, 0x6d,
	0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1a, 0x0a,
	0x08, 0x6c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x08, 0x6c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x32, 0x0a, 0x07, 0x69, 0x6e, 0x76,
	0x69, 0x74, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x43, 0x72, 0x6f, 0x73, 0x73, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x50, 0x6c,
	0x61, 0x79, 0x65, 0x72, 0x52, 0x07, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x72, 0x22, 0x65, 0x0a,
	0x1d, 0x53, 0x32, 0x52, 0x5f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x54, 0x65, 0x61,
	0x6d, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x0e,
	0x0a, 0x02, 0x74, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x74, 0x6f, 0x12, 0x18,
	0x0a, 0x07, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x07, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x6c, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x49, 0x64, 0x22, 0x84, 0x01, 0x0a, 0x1c, 0x53, 0x32, 0x52, 0x5f, 0x42, 0x72, 0x6f,
	0x61, 0x64, 0x63, 0x61, 0x73, 0x74, 0x4a, 0x6f, 0x69, 0x6e, 0x54, 0x65, 0x61, 0x6d, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x61, 0x70, 0x49, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6d, 0x61, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6c,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6c, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x06, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x1e, 0x0a, 0x0a, 0x6c,
	0x6f, 0x63, 0x6b, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0a, 0x6c, 0x6f, 0x63, 0x6b, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x22, 0x70, 0x0a, 0x1a, 0x53,
	0x32, 0x52, 0x5f, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x54, 0x65, 0x61, 0x6d, 0x41, 0x70, 0x70,
	0x6c, 0x79, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x6c, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x36, 0x0a, 0x09, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x43, 0x72, 0x6f, 0x73, 0x73, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x50, 0x6c, 0x61, 0x79,
	0x65, 0x72, 0x52, 0x09, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x22, 0x58, 0x0a,
	0x1c, 0x53, 0x32, 0x52, 0x5f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x54, 0x65, 0x61,
	0x6d, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x6c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x08, 0x6c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x70, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x61, 0x70,
	0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x22, 0x61, 0x0a, 0x19, 0x53, 0x32, 0x52, 0x5f, 0x4e,
	0x6f, 0x74, 0x69, 0x66, 0x79, 0x45, 0x78, 0x69, 0x74, 0x54, 0x65, 0x61, 0x6d, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x61, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x05, 0x6d, 0x61, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6c, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6c, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x06, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x22, 0x84, 0x01, 0x0a, 0x1c, 0x53,
	0x32, 0x52, 0x5f, 0x42, 0x72, 0x6f, 0x61, 0x64, 0x63, 0x61, 0x73, 0x74, 0x45, 0x78, 0x69, 0x74,
	0x54, 0x65, 0x61, 0x6d, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6d,
	0x61, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6d, 0x61, 0x70, 0x49,
	0x64, 0x12, 0x16, 0x0a, 0x06, 0x6c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x06, 0x6c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x65, 0x6d,
	0x62, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6d, 0x65, 0x6d, 0x62, 0x65,
	0x72, 0x12, 0x1e, 0x0a, 0x0a, 0x6c, 0x6f, 0x63, 0x6b, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x6c, 0x6f, 0x63, 0x6b, 0x4d, 0x65, 0x6d, 0x62, 0x65,
	0x72, 0x22, 0x68, 0x0a, 0x20, 0x53, 0x32, 0x52, 0x5f, 0x42, 0x72, 0x6f, 0x61, 0x64, 0x63, 0x61,
	0x73, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x4c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x61, 0x70, 0x49, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6d, 0x61, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6c,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6c, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x06, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x22, 0x4f, 0x0a, 0x1f, 0x53,
	0x32, 0x52, 0x5f, 0x42, 0x72, 0x6f, 0x61, 0x64, 0x63, 0x61, 0x73, 0x74, 0x44, 0x69, 0x73, 0x62,
	0x61, 0x6e, 0x64, 0x54, 0x65, 0x61, 0x6d, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x6d, 0x61, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6d,
	0x61, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0xba, 0x01, 0x0a,
	0x20, 0x53, 0x32, 0x52, 0x5f, 0x42, 0x72, 0x6f, 0x61, 0x64, 0x63, 0x61, 0x73, 0x74, 0x54, 0x65,
	0x61, 0x6d, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x4d, 0x61, 0x70, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x12, 0x1c, 0x0a, 0x09, 0x66, 0x72, 0x6f, 0x6d, 0x4d, 0x61, 0x70, 0x49, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x66, 0x72, 0x6f, 0x6d, 0x4d, 0x61, 0x70, 0x49, 0x64, 0x12,
	0x18, 0x0a, 0x07, 0x74, 0x6f, 0x4d, 0x61, 0x70, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x07, 0x74, 0x6f, 0x4d, 0x61, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x6f, 0x4d,
	0x61, 0x70, 0x58, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x74, 0x6f, 0x4d, 0x61, 0x70,
	0x58, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x6f, 0x4d, 0x61, 0x70, 0x59, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x06, 0x74, 0x6f, 0x4d, 0x61, 0x70, 0x59, 0x12, 0x16, 0x0a, 0x06, 0x6c, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6c, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x06, 0x20, 0x03, 0x28,
	0x05, 0x52, 0x06, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x42, 0x1e, 0x5a, 0x1c, 0x77, 0x6f, 0x72,
	0x6c, 0x64, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x62, 0x43, 0x72, 0x6f, 0x73,
	0x73, 0x3b, 0x70, 0x62, 0x43, 0x72, 0x6f, 0x73, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_pbCross_node_proto_rawDescOnce sync.Once
	file_pbCross_node_proto_rawDescData = file_pbCross_node_proto_rawDesc
)

func file_pbCross_node_proto_rawDescGZIP() []byte {
	file_pbCross_node_proto_rawDescOnce.Do(func() {
		file_pbCross_node_proto_rawDescData = protoimpl.X.CompressGZIP(file_pbCross_node_proto_rawDescData)
	})
	return file_pbCross_node_proto_rawDescData
}

var file_pbCross_node_proto_msgTypes = make([]protoimpl.MessageInfo, 26)
var file_pbCross_node_proto_goTypes = []interface{}{
	(*S2R_OnLeaveMessage)(nil),               // 0: proto.S2R_OnLeaveMessage
	(*S2R_OnPlayerLoginMessage)(nil),         // 1: proto.S2R_OnPlayerLoginMessage
	(*R2S_OnPlayerLoginMessage)(nil),         // 2: proto.R2S_OnPlayerLoginMessage
	(*S2R_IsPlayerOnlineMessage)(nil),        // 3: proto.S2R_IsPlayerOnlineMessage
	(*R2S_IsPlayerOnlineMessage)(nil),        // 4: proto.R2S_IsPlayerOnlineMessage
	(*S2R_KickPlayerForceByPidMessage)(nil),  // 5: proto.S2R_KickPlayerForceByPidMessage
	(*S2R_KickPlayerForceByUidMessage)(nil),  // 6: proto.S2R_KickPlayerForceByUidMessage
	(*S2R_KickSessionByUidMessage)(nil),      // 7: proto.S2R_KickSessionByUidMessage
	(*S2R_RemoveUserByUidMessage)(nil),       // 8: proto.S2R_RemoveUserByUidMessage
	(*R2S_KickPlayerForceMessage)(nil),       // 9: proto.R2S_KickPlayerForceMessage
	(*R2S_SimpleResponseMessage)(nil),        // 10: proto.R2S_SimpleResponseMessage
	(*S2R_NotifyPlayerEnterMapMessage)(nil),  // 11: proto.S2R_NotifyPlayerEnterMapMessage
	(*R2S_ReplayPlayerEnterMapMessage)(nil),  // 12: proto.R2S_ReplayPlayerEnterMapMessage
	(*S2R_NotifyPlayerLeaveMapMessage)(nil),  // 13: proto.S2R_NotifyPlayerLeaveMapMessage
	(*S2R_NotifyPlayerMoveMessage)(nil),      // 14: proto.S2R_NotifyPlayerMoveMessage
	(*S2R_ForwardMessage)(nil),               // 15: proto.S2R_ForwardMessage
	(*S2R_NotifyTeamInviteMessage)(nil),      // 16: proto.S2R_NotifyTeamInviteMessage
	(*S2R_ResponseTeamInviteMessage)(nil),    // 17: proto.S2R_ResponseTeamInviteMessage
	(*S2R_BroadcastJoinTeamMessage)(nil),     // 18: proto.S2R_BroadcastJoinTeamMessage
	(*S2R_NotifyTeamApplyMessage)(nil),       // 19: proto.S2R_NotifyTeamApplyMessage
	(*S2R_ResponseTeamApplyMessage)(nil),     // 20: proto.S2R_ResponseTeamApplyMessage
	(*S2R_NotifyExitTeamMessage)(nil),        // 21: proto.S2R_NotifyExitTeamMessage
	(*S2R_BroadcastExitTeamMessage)(nil),     // 22: proto.S2R_BroadcastExitTeamMessage
	(*S2R_BroadcastChangeLeaderMessage)(nil), // 23: proto.S2R_BroadcastChangeLeaderMessage
	(*S2R_BroadcastDisbandTeamMessage)(nil),  // 24: proto.S2R_BroadcastDisbandTeamMessage
	(*S2R_BroadcastTeamEnterMapMessage)(nil), // 25: proto.S2R_BroadcastTeamEnterMapMessage
	(Response.Code)(0),                       // 26: proto.Response.Code
	(*CrossSimplePlayer)(nil),                // 27: proto.CrossSimplePlayer
}
var file_pbCross_node_proto_depIdxs = []int32{
	26, // 0: proto.R2S_SimpleResponseMessage.code:type_name -> proto.Response.Code
	27, // 1: proto.S2R_NotifyPlayerEnterMapMessage.plr:type_name -> proto.CrossSimplePlayer
	27, // 2: proto.R2S_ReplayPlayerEnterMapMessage.plr:type_name -> proto.CrossSimplePlayer
	27, // 3: proto.S2R_NotifyTeamInviteMessage.inviter:type_name -> proto.CrossSimplePlayer
	27, // 4: proto.S2R_NotifyTeamApplyMessage.applicant:type_name -> proto.CrossSimplePlayer
	5,  // [5:5] is the sub-list for method output_type
	5,  // [5:5] is the sub-list for method input_type
	5,  // [5:5] is the sub-list for extension type_name
	5,  // [5:5] is the sub-list for extension extendee
	0,  // [0:5] is the sub-list for field type_name
}

func init() { file_pbCross_node_proto_init() }
func file_pbCross_node_proto_init() {
	if File_pbCross_node_proto != nil {
		return
	}
	file_pbCross_struct_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_pbCross_node_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2R_OnLeaveMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbCross_node_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2R_OnPlayerLoginMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbCross_node_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*R2S_OnPlayerLoginMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbCross_node_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2R_IsPlayerOnlineMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbCross_node_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*R2S_IsPlayerOnlineMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbCross_node_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2R_KickPlayerForceByPidMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbCross_node_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2R_KickPlayerForceByUidMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbCross_node_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2R_KickSessionByUidMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbCross_node_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2R_RemoveUserByUidMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbCross_node_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*R2S_KickPlayerForceMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbCross_node_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*R2S_SimpleResponseMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbCross_node_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2R_NotifyPlayerEnterMapMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbCross_node_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*R2S_ReplayPlayerEnterMapMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbCross_node_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2R_NotifyPlayerLeaveMapMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbCross_node_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2R_NotifyPlayerMoveMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbCross_node_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2R_ForwardMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbCross_node_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2R_NotifyTeamInviteMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbCross_node_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2R_ResponseTeamInviteMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbCross_node_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2R_BroadcastJoinTeamMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbCross_node_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2R_NotifyTeamApplyMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbCross_node_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2R_ResponseTeamApplyMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbCross_node_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2R_NotifyExitTeamMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbCross_node_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2R_BroadcastExitTeamMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbCross_node_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2R_BroadcastChangeLeaderMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbCross_node_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2R_BroadcastDisbandTeamMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbCross_node_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2R_BroadcastTeamEnterMapMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pbCross_node_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   26,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbCross_node_proto_goTypes,
		DependencyIndexes: file_pbCross_node_proto_depIdxs,
		MessageInfos:      file_pbCross_node_proto_msgTypes,
	}.Build()
	File_pbCross_node_proto = out.File
	file_pbCross_node_proto_rawDesc = nil
	file_pbCross_node_proto_goTypes = nil
	file_pbCross_node_proto_depIdxs = nil
}
