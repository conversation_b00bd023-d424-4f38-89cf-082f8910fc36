// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v6.30.1
// source: pbGame/ITEM_ID/ItemId.proto

package ITEM_ID

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// After are enums.
// 物品id定义
type Type int32

const (
	ID_NONE                       Type = 0     //无效物品
	PET                           Type = 24    //宠物
	WOOD                          Type = 1000  //木头
	STONE                         Type = 1001  //石头
	IRON                          Type = 1002  //铁
	IDENTIFY_SCROLL               Type = 40000 //鉴定卷轴
	IDENTIFY_SCROLL_BIND          Type = 40001 //鉴定卷轴(绑定)
	COMMAND_BOOK                  Type = 40002 //指令书
	PET_RESET                     Type = 40004 //宠物洗髓石
	PET_AGE                       Type = 40005 //宠物返老还童石
	REPAIR                        Type = 40008 //野外修理卷
	PET_RESET_2                   Type = 40015 //宠物重生石
	CHANGE_NAME                   Type = 40016 //改名卷
	SPEAK                         Type = 40017 //小喇叭
	PET_ADD_SKILL                 Type = 40020 //宠物潜能石
	STAR_SCROLL                   Type = 40021 //装备强化卷
	CHANGE_SEX                    Type = 42000 //变性卷轴
	CP_POINT_ADD                  Type = 42001 //属性点道具
	SP_POINT_ADD                  Type = 42002 //技能点道具
	PROSPERITY_DEGREE_POINT_ADD   Type = 42003 //繁荣度
	SKILL_PLAYER                  Type = 42004 //人物技能槽
	SKILL_PET                     Type = 42005 //宠物技能卷槽
	SKILL_PLAYER_2                Type = 42006 //人物技能卷槽(绑)
	SKILL_PET_2                   Type = 42007 //宠物技能卷槽(绑)
	HIGH_IDENTIFY_SCROLL          Type = 40022 //高级鉴定卷轴
	HIGH_IDENTIFY_SCROLL_BIND     Type = 40023 //高级鉴定卷轴(绑)
	UPGRADE_IDENTIFY_SCROLL       Type = 40024 //进阶鉴定卷轴
	UPGRADE_IDENTIFY_SCROLL_BIND  Type = 40025 //进阶鉴定卷轴(绑)
	UPGRADE_INTENSIFY_SCROLL      Type = 40026 //进阶强化卷轴
	UPGRADE_INTENSIFY_SCROLL_BIND Type = 40027 //进阶强化卷轴(绑)
	PET_EXPERIENCE_BOOK           Type = 40047 //宠物经验卷
	PET_EXPERIENCE_BOOK2          Type = 40043 //宠物经验卷
	PET_EXPERIENCE_BOOK3          Type = 40006 //宠物经验卷
	SPEAK2                        Type = 42016 //跨服小喇叭
	SKILL_PLAYER_3                Type = 42017 //中级人物技能槽
	SKILL_PET_3                   Type = 42018 //中级宠物技能槽(绑)
	ADD_BAG_SIZE                  Type = 288   //10格背包扩展
	HAIR_START                    Type = 40100 //
	HAIR_END                      Type = 40199 //
	FACE_START                    Type = 40200 //
	FACE_END                      Type = 40249 //
	RANGER                        Type = 41100 //转职书-侠客
	XIUZHEN                       Type = 41101 //转职书-修真
	WARRIOR                       Type = 41102 //转职书-战士
	WIZARD                        Type = 41103 //转职书-法师
	NEW                           Type = 41104 //转职书-贤者
	BACKUP2                       Type = 41105 //转职书-武圣
	BACKUP3                       Type = 41106 //转职书-枪王
	BACKUP4                       Type = 41107 //转职书-锤师
	BACKUP5                       Type = 41108 //转职书-岚舞
	BACKUP6                       Type = 41109 //转职书-备用
	BACKUP7                       Type = 41110 //转职书-备用
)

// Enum value maps for Type.
var (
	Type_name = map[int32]string{
		0:     "ID_NONE",
		24:    "PET",
		1000:  "WOOD",
		1001:  "STONE",
		1002:  "IRON",
		40000: "IDENTIFY_SCROLL",
		40001: "IDENTIFY_SCROLL_BIND",
		40002: "COMMAND_BOOK",
		40004: "PET_RESET",
		40005: "PET_AGE",
		40008: "REPAIR",
		40015: "PET_RESET_2",
		40016: "CHANGE_NAME",
		40017: "SPEAK",
		40020: "PET_ADD_SKILL",
		40021: "STAR_SCROLL",
		42000: "CHANGE_SEX",
		42001: "CP_POINT_ADD",
		42002: "SP_POINT_ADD",
		42003: "PROSPERITY_DEGREE_POINT_ADD",
		42004: "SKILL_PLAYER",
		42005: "SKILL_PET",
		42006: "SKILL_PLAYER_2",
		42007: "SKILL_PET_2",
		40022: "HIGH_IDENTIFY_SCROLL",
		40023: "HIGH_IDENTIFY_SCROLL_BIND",
		40024: "UPGRADE_IDENTIFY_SCROLL",
		40025: "UPGRADE_IDENTIFY_SCROLL_BIND",
		40026: "UPGRADE_INTENSIFY_SCROLL",
		40027: "UPGRADE_INTENSIFY_SCROLL_BIND",
		40047: "PET_EXPERIENCE_BOOK",
		40043: "PET_EXPERIENCE_BOOK2",
		40006: "PET_EXPERIENCE_BOOK3",
		42016: "SPEAK2",
		42017: "SKILL_PLAYER_3",
		42018: "SKILL_PET_3",
		288:   "ADD_BAG_SIZE",
		40100: "HAIR_START",
		40199: "HAIR_END",
		40200: "FACE_START",
		40249: "FACE_END",
		41100: "RANGER",
		41101: "XIUZHEN",
		41102: "WARRIOR",
		41103: "WIZARD",
		41104: "NEW",
		41105: "BACKUP2",
		41106: "BACKUP3",
		41107: "BACKUP4",
		41108: "BACKUP5",
		41109: "BACKUP6",
		41110: "BACKUP7",
	}
	Type_value = map[string]int32{
		"ID_NONE":                       0,
		"PET":                           24,
		"WOOD":                          1000,
		"STONE":                         1001,
		"IRON":                          1002,
		"IDENTIFY_SCROLL":               40000,
		"IDENTIFY_SCROLL_BIND":          40001,
		"COMMAND_BOOK":                  40002,
		"PET_RESET":                     40004,
		"PET_AGE":                       40005,
		"REPAIR":                        40008,
		"PET_RESET_2":                   40015,
		"CHANGE_NAME":                   40016,
		"SPEAK":                         40017,
		"PET_ADD_SKILL":                 40020,
		"STAR_SCROLL":                   40021,
		"CHANGE_SEX":                    42000,
		"CP_POINT_ADD":                  42001,
		"SP_POINT_ADD":                  42002,
		"PROSPERITY_DEGREE_POINT_ADD":   42003,
		"SKILL_PLAYER":                  42004,
		"SKILL_PET":                     42005,
		"SKILL_PLAYER_2":                42006,
		"SKILL_PET_2":                   42007,
		"HIGH_IDENTIFY_SCROLL":          40022,
		"HIGH_IDENTIFY_SCROLL_BIND":     40023,
		"UPGRADE_IDENTIFY_SCROLL":       40024,
		"UPGRADE_IDENTIFY_SCROLL_BIND":  40025,
		"UPGRADE_INTENSIFY_SCROLL":      40026,
		"UPGRADE_INTENSIFY_SCROLL_BIND": 40027,
		"PET_EXPERIENCE_BOOK":           40047,
		"PET_EXPERIENCE_BOOK2":          40043,
		"PET_EXPERIENCE_BOOK3":          40006,
		"SPEAK2":                        42016,
		"SKILL_PLAYER_3":                42017,
		"SKILL_PET_3":                   42018,
		"ADD_BAG_SIZE":                  288,
		"HAIR_START":                    40100,
		"HAIR_END":                      40199,
		"FACE_START":                    40200,
		"FACE_END":                      40249,
		"RANGER":                        41100,
		"XIUZHEN":                       41101,
		"WARRIOR":                       41102,
		"WIZARD":                        41103,
		"NEW":                           41104,
		"BACKUP2":                       41105,
		"BACKUP3":                       41106,
		"BACKUP4":                       41107,
		"BACKUP5":                       41108,
		"BACKUP6":                       41109,
		"BACKUP7":                       41110,
	}
)

func (x Type) Enum() *Type {
	p := new(Type)
	*p = x
	return p
}

func (x Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Type) Descriptor() protoreflect.EnumDescriptor {
	return file_pbGame_ITEM_ID_ItemId_proto_enumTypes[0].Descriptor()
}

func (Type) Type() protoreflect.EnumType {
	return &file_pbGame_ITEM_ID_ItemId_proto_enumTypes[0]
}

func (x Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Type.Descriptor instead.
func (Type) EnumDescriptor() ([]byte, []int) {
	return file_pbGame_ITEM_ID_ItemId_proto_rawDescGZIP(), []int{0}
}

var File_pbGame_ITEM_ID_ItemId_proto protoreflect.FileDescriptor

var file_pbGame_ITEM_ID_ItemId_proto_rawDesc = []byte{
	0x0a, 0x1b, 0x70, 0x62, 0x47, 0x61, 0x6d, 0x65, 0x2f, 0x49, 0x54, 0x45, 0x4d, 0x5f, 0x49, 0x44,
	0x2f, 0x49, 0x74, 0x65, 0x6d, 0x49, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0d, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x49, 0x54, 0x45, 0x4d, 0x5f, 0x49, 0x44, 0x2a, 0xfe, 0x07, 0x0a,
	0x04, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x49, 0x44, 0x5f, 0x4e, 0x4f, 0x4e, 0x45,
	0x10, 0x00, 0x12, 0x07, 0x0a, 0x03, 0x50, 0x45, 0x54, 0x10, 0x18, 0x12, 0x09, 0x0a, 0x04, 0x57,
	0x4f, 0x4f, 0x44, 0x10, 0xe8, 0x07, 0x12, 0x0a, 0x0a, 0x05, 0x53, 0x54, 0x4f, 0x4e, 0x45, 0x10,
	0xe9, 0x07, 0x12, 0x09, 0x0a, 0x04, 0x49, 0x52, 0x4f, 0x4e, 0x10, 0xea, 0x07, 0x12, 0x15, 0x0a,
	0x0f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x59, 0x5f, 0x53, 0x43, 0x52, 0x4f, 0x4c, 0x4c,
	0x10, 0xc0, 0xb8, 0x02, 0x12, 0x1a, 0x0a, 0x14, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x59,
	0x5f, 0x53, 0x43, 0x52, 0x4f, 0x4c, 0x4c, 0x5f, 0x42, 0x49, 0x4e, 0x44, 0x10, 0xc1, 0xb8, 0x02,
	0x12, 0x12, 0x0a, 0x0c, 0x43, 0x4f, 0x4d, 0x4d, 0x41, 0x4e, 0x44, 0x5f, 0x42, 0x4f, 0x4f, 0x4b,
	0x10, 0xc2, 0xb8, 0x02, 0x12, 0x0f, 0x0a, 0x09, 0x50, 0x45, 0x54, 0x5f, 0x52, 0x45, 0x53, 0x45,
	0x54, 0x10, 0xc4, 0xb8, 0x02, 0x12, 0x0d, 0x0a, 0x07, 0x50, 0x45, 0x54, 0x5f, 0x41, 0x47, 0x45,
	0x10, 0xc5, 0xb8, 0x02, 0x12, 0x0c, 0x0a, 0x06, 0x52, 0x45, 0x50, 0x41, 0x49, 0x52, 0x10, 0xc8,
	0xb8, 0x02, 0x12, 0x11, 0x0a, 0x0b, 0x50, 0x45, 0x54, 0x5f, 0x52, 0x45, 0x53, 0x45, 0x54, 0x5f,
	0x32, 0x10, 0xcf, 0xb8, 0x02, 0x12, 0x11, 0x0a, 0x0b, 0x43, 0x48, 0x41, 0x4e, 0x47, 0x45, 0x5f,
	0x4e, 0x41, 0x4d, 0x45, 0x10, 0xd0, 0xb8, 0x02, 0x12, 0x0b, 0x0a, 0x05, 0x53, 0x50, 0x45, 0x41,
	0x4b, 0x10, 0xd1, 0xb8, 0x02, 0x12, 0x13, 0x0a, 0x0d, 0x50, 0x45, 0x54, 0x5f, 0x41, 0x44, 0x44,
	0x5f, 0x53, 0x4b, 0x49, 0x4c, 0x4c, 0x10, 0xd4, 0xb8, 0x02, 0x12, 0x11, 0x0a, 0x0b, 0x53, 0x54,
	0x41, 0x52, 0x5f, 0x53, 0x43, 0x52, 0x4f, 0x4c, 0x4c, 0x10, 0xd5, 0xb8, 0x02, 0x12, 0x10, 0x0a,
	0x0a, 0x43, 0x48, 0x41, 0x4e, 0x47, 0x45, 0x5f, 0x53, 0x45, 0x58, 0x10, 0x90, 0xc8, 0x02, 0x12,
	0x12, 0x0a, 0x0c, 0x43, 0x50, 0x5f, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x5f, 0x41, 0x44, 0x44, 0x10,
	0x91, 0xc8, 0x02, 0x12, 0x12, 0x0a, 0x0c, 0x53, 0x50, 0x5f, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x5f,
	0x41, 0x44, 0x44, 0x10, 0x92, 0xc8, 0x02, 0x12, 0x21, 0x0a, 0x1b, 0x50, 0x52, 0x4f, 0x53, 0x50,
	0x45, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x44, 0x45, 0x47, 0x52, 0x45, 0x45, 0x5f, 0x50, 0x4f, 0x49,
	0x4e, 0x54, 0x5f, 0x41, 0x44, 0x44, 0x10, 0x93, 0xc8, 0x02, 0x12, 0x12, 0x0a, 0x0c, 0x53, 0x4b,
	0x49, 0x4c, 0x4c, 0x5f, 0x50, 0x4c, 0x41, 0x59, 0x45, 0x52, 0x10, 0x94, 0xc8, 0x02, 0x12, 0x0f,
	0x0a, 0x09, 0x53, 0x4b, 0x49, 0x4c, 0x4c, 0x5f, 0x50, 0x45, 0x54, 0x10, 0x95, 0xc8, 0x02, 0x12,
	0x14, 0x0a, 0x0e, 0x53, 0x4b, 0x49, 0x4c, 0x4c, 0x5f, 0x50, 0x4c, 0x41, 0x59, 0x45, 0x52, 0x5f,
	0x32, 0x10, 0x96, 0xc8, 0x02, 0x12, 0x11, 0x0a, 0x0b, 0x53, 0x4b, 0x49, 0x4c, 0x4c, 0x5f, 0x50,
	0x45, 0x54, 0x5f, 0x32, 0x10, 0x97, 0xc8, 0x02, 0x12, 0x1a, 0x0a, 0x14, 0x48, 0x49, 0x47, 0x48,
	0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x59, 0x5f, 0x53, 0x43, 0x52, 0x4f, 0x4c, 0x4c,
	0x10, 0xd6, 0xb8, 0x02, 0x12, 0x1f, 0x0a, 0x19, 0x48, 0x49, 0x47, 0x48, 0x5f, 0x49, 0x44, 0x45,
	0x4e, 0x54, 0x49, 0x46, 0x59, 0x5f, 0x53, 0x43, 0x52, 0x4f, 0x4c, 0x4c, 0x5f, 0x42, 0x49, 0x4e,
	0x44, 0x10, 0xd7, 0xb8, 0x02, 0x12, 0x1d, 0x0a, 0x17, 0x55, 0x50, 0x47, 0x52, 0x41, 0x44, 0x45,
	0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x59, 0x5f, 0x53, 0x43, 0x52, 0x4f, 0x4c, 0x4c,
	0x10, 0xd8, 0xb8, 0x02, 0x12, 0x22, 0x0a, 0x1c, 0x55, 0x50, 0x47, 0x52, 0x41, 0x44, 0x45, 0x5f,
	0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x59, 0x5f, 0x53, 0x43, 0x52, 0x4f, 0x4c, 0x4c, 0x5f,
	0x42, 0x49, 0x4e, 0x44, 0x10, 0xd9, 0xb8, 0x02, 0x12, 0x1e, 0x0a, 0x18, 0x55, 0x50, 0x47, 0x52,
	0x41, 0x44, 0x45, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x4e, 0x53, 0x49, 0x46, 0x59, 0x5f, 0x53, 0x43,
	0x52, 0x4f, 0x4c, 0x4c, 0x10, 0xda, 0xb8, 0x02, 0x12, 0x23, 0x0a, 0x1d, 0x55, 0x50, 0x47, 0x52,
	0x41, 0x44, 0x45, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x4e, 0x53, 0x49, 0x46, 0x59, 0x5f, 0x53, 0x43,
	0x52, 0x4f, 0x4c, 0x4c, 0x5f, 0x42, 0x49, 0x4e, 0x44, 0x10, 0xdb, 0xb8, 0x02, 0x12, 0x19, 0x0a,
	0x13, 0x50, 0x45, 0x54, 0x5f, 0x45, 0x58, 0x50, 0x45, 0x52, 0x49, 0x45, 0x4e, 0x43, 0x45, 0x5f,
	0x42, 0x4f, 0x4f, 0x4b, 0x10, 0xef, 0xb8, 0x02, 0x12, 0x1a, 0x0a, 0x14, 0x50, 0x45, 0x54, 0x5f,
	0x45, 0x58, 0x50, 0x45, 0x52, 0x49, 0x45, 0x4e, 0x43, 0x45, 0x5f, 0x42, 0x4f, 0x4f, 0x4b, 0x32,
	0x10, 0xeb, 0xb8, 0x02, 0x12, 0x1a, 0x0a, 0x14, 0x50, 0x45, 0x54, 0x5f, 0x45, 0x58, 0x50, 0x45,
	0x52, 0x49, 0x45, 0x4e, 0x43, 0x45, 0x5f, 0x42, 0x4f, 0x4f, 0x4b, 0x33, 0x10, 0xc6, 0xb8, 0x02,
	0x12, 0x0c, 0x0a, 0x06, 0x53, 0x50, 0x45, 0x41, 0x4b, 0x32, 0x10, 0xa0, 0xc8, 0x02, 0x12, 0x14,
	0x0a, 0x0e, 0x53, 0x4b, 0x49, 0x4c, 0x4c, 0x5f, 0x50, 0x4c, 0x41, 0x59, 0x45, 0x52, 0x5f, 0x33,
	0x10, 0xa1, 0xc8, 0x02, 0x12, 0x11, 0x0a, 0x0b, 0x53, 0x4b, 0x49, 0x4c, 0x4c, 0x5f, 0x50, 0x45,
	0x54, 0x5f, 0x33, 0x10, 0xa2, 0xc8, 0x02, 0x12, 0x11, 0x0a, 0x0c, 0x41, 0x44, 0x44, 0x5f, 0x42,
	0x41, 0x47, 0x5f, 0x53, 0x49, 0x5a, 0x45, 0x10, 0xa0, 0x02, 0x12, 0x10, 0x0a, 0x0a, 0x48, 0x41,
	0x49, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x52, 0x54, 0x10, 0xa4, 0xb9, 0x02, 0x12, 0x0e, 0x0a, 0x08,
	0x48, 0x41, 0x49, 0x52, 0x5f, 0x45, 0x4e, 0x44, 0x10, 0x87, 0xba, 0x02, 0x12, 0x10, 0x0a, 0x0a,
	0x46, 0x41, 0x43, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x52, 0x54, 0x10, 0x88, 0xba, 0x02, 0x12, 0x0e,
	0x0a, 0x08, 0x46, 0x41, 0x43, 0x45, 0x5f, 0x45, 0x4e, 0x44, 0x10, 0xb9, 0xba, 0x02, 0x12, 0x0c,
	0x0a, 0x06, 0x52, 0x41, 0x4e, 0x47, 0x45, 0x52, 0x10, 0x8c, 0xc1, 0x02, 0x12, 0x0d, 0x0a, 0x07,
	0x58, 0x49, 0x55, 0x5a, 0x48, 0x45, 0x4e, 0x10, 0x8d, 0xc1, 0x02, 0x12, 0x0d, 0x0a, 0x07, 0x57,
	0x41, 0x52, 0x52, 0x49, 0x4f, 0x52, 0x10, 0x8e, 0xc1, 0x02, 0x12, 0x0c, 0x0a, 0x06, 0x57, 0x49,
	0x5a, 0x41, 0x52, 0x44, 0x10, 0x8f, 0xc1, 0x02, 0x12, 0x09, 0x0a, 0x03, 0x4e, 0x45, 0x57, 0x10,
	0x90, 0xc1, 0x02, 0x12, 0x0d, 0x0a, 0x07, 0x42, 0x41, 0x43, 0x4b, 0x55, 0x50, 0x32, 0x10, 0x91,
	0xc1, 0x02, 0x12, 0x0d, 0x0a, 0x07, 0x42, 0x41, 0x43, 0x4b, 0x55, 0x50, 0x33, 0x10, 0x92, 0xc1,
	0x02, 0x12, 0x0d, 0x0a, 0x07, 0x42, 0x41, 0x43, 0x4b, 0x55, 0x50, 0x34, 0x10, 0x93, 0xc1, 0x02,
	0x12, 0x0d, 0x0a, 0x07, 0x42, 0x41, 0x43, 0x4b, 0x55, 0x50, 0x35, 0x10, 0x94, 0xc1, 0x02, 0x12,
	0x0d, 0x0a, 0x07, 0x42, 0x41, 0x43, 0x4b, 0x55, 0x50, 0x36, 0x10, 0x95, 0xc1, 0x02, 0x12, 0x0d,
	0x0a, 0x07, 0x42, 0x41, 0x43, 0x4b, 0x55, 0x50, 0x37, 0x10, 0x96, 0xc1, 0x02, 0x42, 0x25, 0x5a,
	0x23, 0x77, 0x6f, 0x72, 0x6c, 0x64, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x62,
	0x47, 0x61, 0x6d, 0x65, 0x2f, 0x49, 0x54, 0x45, 0x4d, 0x5f, 0x49, 0x44, 0x3b, 0x49, 0x54, 0x45,
	0x4d, 0x5f, 0x49, 0x44, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pbGame_ITEM_ID_ItemId_proto_rawDescOnce sync.Once
	file_pbGame_ITEM_ID_ItemId_proto_rawDescData = file_pbGame_ITEM_ID_ItemId_proto_rawDesc
)

func file_pbGame_ITEM_ID_ItemId_proto_rawDescGZIP() []byte {
	file_pbGame_ITEM_ID_ItemId_proto_rawDescOnce.Do(func() {
		file_pbGame_ITEM_ID_ItemId_proto_rawDescData = protoimpl.X.CompressGZIP(file_pbGame_ITEM_ID_ItemId_proto_rawDescData)
	})
	return file_pbGame_ITEM_ID_ItemId_proto_rawDescData
}

var file_pbGame_ITEM_ID_ItemId_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_pbGame_ITEM_ID_ItemId_proto_goTypes = []interface{}{
	(Type)(0), // 0: proto.ITEM_ID.Type
}
var file_pbGame_ITEM_ID_ItemId_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pbGame_ITEM_ID_ItemId_proto_init() }
func file_pbGame_ITEM_ID_ItemId_proto_init() {
	if File_pbGame_ITEM_ID_ItemId_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pbGame_ITEM_ID_ItemId_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbGame_ITEM_ID_ItemId_proto_goTypes,
		DependencyIndexes: file_pbGame_ITEM_ID_ItemId_proto_depIdxs,
		EnumInfos:         file_pbGame_ITEM_ID_ItemId_proto_enumTypes,
	}.Build()
	File_pbGame_ITEM_ID_ItemId_proto = out.File
	file_pbGame_ITEM_ID_ItemId_proto_rawDesc = nil
	file_pbGame_ITEM_ID_ItemId_proto_goTypes = nil
	file_pbGame_ITEM_ID_ItemId_proto_depIdxs = nil
}
