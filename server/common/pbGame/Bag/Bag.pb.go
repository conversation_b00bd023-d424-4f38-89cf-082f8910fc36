// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v6.30.1
// source: pbGame/Bag/Bag.proto

package Bag

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// After are enums.
// 使用物品时的类型定义
type ItemUseType int32

const (
	None        ItemUseType = 0 //未知
	Equip       ItemUseType = 1 //装备
	UnEquip     ItemUseType = 2 //
	Use         ItemUseType = 3 //
	Lose        ItemUseType = 4 //
	CheckUp     ItemUseType = 5 //
	Enchase     ItemUseType = 6 //
	Bind        ItemUseType = 7 //
	UseByOneKey ItemUseType = 8 //
)

// Enum value maps for ItemUseType.
var (
	ItemUseType_name = map[int32]string{
		0: "None",
		1: "Equip",
		2: "UnEquip",
		3: "Use",
		4: "Lose",
		5: "CheckUp",
		6: "Enchase",
		7: "Bind",
		8: "UseByOneKey",
	}
	ItemUseType_value = map[string]int32{
		"None":        0,
		"Equip":       1,
		"UnEquip":     2,
		"Use":         3,
		"Lose":        4,
		"CheckUp":     5,
		"Enchase":     6,
		"Bind":        7,
		"UseByOneKey": 8,
	}
)

func (x ItemUseType) Enum() *ItemUseType {
	p := new(ItemUseType)
	*p = x
	return p
}

func (x ItemUseType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ItemUseType) Descriptor() protoreflect.EnumDescriptor {
	return file_pbGame_Bag_Bag_proto_enumTypes[0].Descriptor()
}

func (ItemUseType) Type() protoreflect.EnumType {
	return &file_pbGame_Bag_Bag_proto_enumTypes[0]
}

func (x ItemUseType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ItemUseType.Descriptor instead.
func (ItemUseType) EnumDescriptor() ([]byte, []int) {
	return file_pbGame_Bag_Bag_proto_rawDescGZIP(), []int{0}
}

// 使用物品时的类型定义

type ItemUseSubType int32

const (
	NoWait              ItemUseSubType = 0  //无需等待结果
	UsePetEgg           ItemUseSubType = 1  //使用宠物蛋
	EquipPet            ItemUseSubType = 2  //装备宠物
	UnEquipPet          ItemUseSubType = 3  //卸下宠物
	UseChest            ItemUseSubType = 4  //开启奖励盒子
	CheckUpSub          ItemUseSubType = 5  //
	EnchaseSub          ItemUseSubType = 6  //附魔
	BindSub             ItemUseSubType = 7  //绑定
	CommandBook         ItemUseSubType = 8  //使用指令书
	EquipSub            ItemUseSubType = 9  //装备
	AddStoreNum         ItemUseSubType = 10 //增加仓库格子数
	AddExp              ItemUseSubType = 11 //增加经验
	AddPetExp           ItemUseSubType = 12 //增加宠物经验
	PetRest             ItemUseSubType = 13 //宠物洗髓
	PetAge              ItemUseSubType = 14 //恢复宠物寿命
	Repair              ItemUseSubType = 15 //修理
	GetTitle            ItemUseSubType = 16 //获取称号
	UnEquipSub          ItemUseSubType = 17 //卸下装备
	ChangeJob           ItemUseSubType = 18 //更换职业
	PetItemAddSkill     ItemUseSubType = 19 //宠物潜能石头
	Wait                ItemUseSubType = 20 //
	AlertSex            ItemUseSubType = 21 //
	AddCp               ItemUseSubType = 22 //增加属性点
	AddSp               ItemUseSubType = 23 //增加技能点
	AddProsperityDegree ItemUseSubType = 24 //
	SkillSlotPlayer     ItemUseSubType = 25 //人物技能槽
	SkillSlotPet        ItemUseSubType = 26 //宠物技能槽
	PetAddLife          ItemUseSubType = 27 //恢复宠物寿命
)

// Enum value maps for ItemUseSubType.
var (
	ItemUseSubType_name = map[int32]string{
		0:  "NoWait",
		1:  "UsePetEgg",
		2:  "EquipPet",
		3:  "UnEquipPet",
		4:  "UseChest",
		5:  "CheckUpSub",
		6:  "EnchaseSub",
		7:  "BindSub",
		8:  "CommandBook",
		9:  "EquipSub",
		10: "AddStoreNum",
		11: "AddExp",
		12: "AddPetExp",
		13: "PetRest",
		14: "PetAge",
		15: "Repair",
		16: "GetTitle",
		17: "UnEquipSub",
		18: "ChangeJob",
		19: "PetItemAddSkill",
		20: "Wait",
		21: "AlertSex",
		22: "AddCp",
		23: "AddSp",
		24: "AddProsperityDegree",
		25: "SkillSlotPlayer",
		26: "SkillSlotPet",
		27: "PetAddLife",
	}
	ItemUseSubType_value = map[string]int32{
		"NoWait":              0,
		"UsePetEgg":           1,
		"EquipPet":            2,
		"UnEquipPet":          3,
		"UseChest":            4,
		"CheckUpSub":          5,
		"EnchaseSub":          6,
		"BindSub":             7,
		"CommandBook":         8,
		"EquipSub":            9,
		"AddStoreNum":         10,
		"AddExp":              11,
		"AddPetExp":           12,
		"PetRest":             13,
		"PetAge":              14,
		"Repair":              15,
		"GetTitle":            16,
		"UnEquipSub":          17,
		"ChangeJob":           18,
		"PetItemAddSkill":     19,
		"Wait":                20,
		"AlertSex":            21,
		"AddCp":               22,
		"AddSp":               23,
		"AddProsperityDegree": 24,
		"SkillSlotPlayer":     25,
		"SkillSlotPet":        26,
		"PetAddLife":          27,
	}
)

func (x ItemUseSubType) Enum() *ItemUseSubType {
	p := new(ItemUseSubType)
	*p = x
	return p
}

func (x ItemUseSubType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ItemUseSubType) Descriptor() protoreflect.EnumDescriptor {
	return file_pbGame_Bag_Bag_proto_enumTypes[1].Descriptor()
}

func (ItemUseSubType) Type() protoreflect.EnumType {
	return &file_pbGame_Bag_Bag_proto_enumTypes[1]
}

func (x ItemUseSubType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ItemUseSubType.Descriptor instead.
func (ItemUseSubType) EnumDescriptor() ([]byte, []int) {
	return file_pbGame_Bag_Bag_proto_rawDescGZIP(), []int{1}
}

var File_pbGame_Bag_Bag_proto protoreflect.FileDescriptor

var file_pbGame_Bag_Bag_proto_rawDesc = []byte{
	0x0a, 0x14, 0x70, 0x62, 0x47, 0x61, 0x6d, 0x65, 0x2f, 0x42, 0x61, 0x67, 0x2f, 0x42, 0x61, 0x67,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x09, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x42, 0x61,
	0x67, 0x2a, 0x77, 0x0a, 0x0b, 0x49, 0x74, 0x65, 0x6d, 0x55, 0x73, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x08, 0x0a, 0x04, 0x4e, 0x6f, 0x6e, 0x65, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x45, 0x71,
	0x75, 0x69, 0x70, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x6e, 0x45, 0x71, 0x75, 0x69, 0x70,
	0x10, 0x02, 0x12, 0x07, 0x0a, 0x03, 0x55, 0x73, 0x65, 0x10, 0x03, 0x12, 0x08, 0x0a, 0x04, 0x4c,
	0x6f, 0x73, 0x65, 0x10, 0x04, 0x12, 0x0b, 0x0a, 0x07, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x55, 0x70,
	0x10, 0x05, 0x12, 0x0b, 0x0a, 0x07, 0x45, 0x6e, 0x63, 0x68, 0x61, 0x73, 0x65, 0x10, 0x06, 0x12,
	0x08, 0x0a, 0x04, 0x42, 0x69, 0x6e, 0x64, 0x10, 0x07, 0x12, 0x0f, 0x0a, 0x0b, 0x55, 0x73, 0x65,
	0x42, 0x79, 0x4f, 0x6e, 0x65, 0x4b, 0x65, 0x79, 0x10, 0x08, 0x2a, 0xb4, 0x03, 0x0a, 0x0e, 0x49,
	0x74, 0x65, 0x6d, 0x55, 0x73, 0x65, 0x53, 0x75, 0x62, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0a, 0x0a,
	0x06, 0x4e, 0x6f, 0x57, 0x61, 0x69, 0x74, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x55, 0x73, 0x65,
	0x50, 0x65, 0x74, 0x45, 0x67, 0x67, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x45, 0x71, 0x75, 0x69,
	0x70, 0x50, 0x65, 0x74, 0x10, 0x02, 0x12, 0x0e, 0x0a, 0x0a, 0x55, 0x6e, 0x45, 0x71, 0x75, 0x69,
	0x70, 0x50, 0x65, 0x74, 0x10, 0x03, 0x12, 0x0c, 0x0a, 0x08, 0x55, 0x73, 0x65, 0x43, 0x68, 0x65,
	0x73, 0x74, 0x10, 0x04, 0x12, 0x0e, 0x0a, 0x0a, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x55, 0x70, 0x53,
	0x75, 0x62, 0x10, 0x05, 0x12, 0x0e, 0x0a, 0x0a, 0x45, 0x6e, 0x63, 0x68, 0x61, 0x73, 0x65, 0x53,
	0x75, 0x62, 0x10, 0x06, 0x12, 0x0b, 0x0a, 0x07, 0x42, 0x69, 0x6e, 0x64, 0x53, 0x75, 0x62, 0x10,
	0x07, 0x12, 0x0f, 0x0a, 0x0b, 0x43, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x42, 0x6f, 0x6f, 0x6b,
	0x10, 0x08, 0x12, 0x0c, 0x0a, 0x08, 0x45, 0x71, 0x75, 0x69, 0x70, 0x53, 0x75, 0x62, 0x10, 0x09,
	0x12, 0x0f, 0x0a, 0x0b, 0x41, 0x64, 0x64, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x4e, 0x75, 0x6d, 0x10,
	0x0a, 0x12, 0x0a, 0x0a, 0x06, 0x41, 0x64, 0x64, 0x45, 0x78, 0x70, 0x10, 0x0b, 0x12, 0x0d, 0x0a,
	0x09, 0x41, 0x64, 0x64, 0x50, 0x65, 0x74, 0x45, 0x78, 0x70, 0x10, 0x0c, 0x12, 0x0b, 0x0a, 0x07,
	0x50, 0x65, 0x74, 0x52, 0x65, 0x73, 0x74, 0x10, 0x0d, 0x12, 0x0a, 0x0a, 0x06, 0x50, 0x65, 0x74,
	0x41, 0x67, 0x65, 0x10, 0x0e, 0x12, 0x0a, 0x0a, 0x06, 0x52, 0x65, 0x70, 0x61, 0x69, 0x72, 0x10,
	0x0f, 0x12, 0x0c, 0x0a, 0x08, 0x47, 0x65, 0x74, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x10, 0x10, 0x12,
	0x0e, 0x0a, 0x0a, 0x55, 0x6e, 0x45, 0x71, 0x75, 0x69, 0x70, 0x53, 0x75, 0x62, 0x10, 0x11, 0x12,
	0x0d, 0x0a, 0x09, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x4a, 0x6f, 0x62, 0x10, 0x12, 0x12, 0x13,
	0x0a, 0x0f, 0x50, 0x65, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x41, 0x64, 0x64, 0x53, 0x6b, 0x69, 0x6c,
	0x6c, 0x10, 0x13, 0x12, 0x08, 0x0a, 0x04, 0x57, 0x61, 0x69, 0x74, 0x10, 0x14, 0x12, 0x0c, 0x0a,
	0x08, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x53, 0x65, 0x78, 0x10, 0x15, 0x12, 0x09, 0x0a, 0x05, 0x41,
	0x64, 0x64, 0x43, 0x70, 0x10, 0x16, 0x12, 0x09, 0x0a, 0x05, 0x41, 0x64, 0x64, 0x53, 0x70, 0x10,
	0x17, 0x12, 0x17, 0x0a, 0x13, 0x41, 0x64, 0x64, 0x50, 0x72, 0x6f, 0x73, 0x70, 0x65, 0x72, 0x69,
	0x74, 0x79, 0x44, 0x65, 0x67, 0x72, 0x65, 0x65, 0x10, 0x18, 0x12, 0x13, 0x0a, 0x0f, 0x53, 0x6b,
	0x69, 0x6c, 0x6c, 0x53, 0x6c, 0x6f, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x10, 0x19, 0x12,
	0x10, 0x0a, 0x0c, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x53, 0x6c, 0x6f, 0x74, 0x50, 0x65, 0x74, 0x10,
	0x1a, 0x12, 0x0e, 0x0a, 0x0a, 0x50, 0x65, 0x74, 0x41, 0x64, 0x64, 0x4c, 0x69, 0x66, 0x65, 0x10,
	0x1b, 0x42, 0x1d, 0x5a, 0x1b, 0x77, 0x6f, 0x72, 0x6c, 0x64, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2f, 0x70, 0x62, 0x47, 0x61, 0x6d, 0x65, 0x2f, 0x42, 0x61, 0x67, 0x3b, 0x42, 0x61, 0x67,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pbGame_Bag_Bag_proto_rawDescOnce sync.Once
	file_pbGame_Bag_Bag_proto_rawDescData = file_pbGame_Bag_Bag_proto_rawDesc
)

func file_pbGame_Bag_Bag_proto_rawDescGZIP() []byte {
	file_pbGame_Bag_Bag_proto_rawDescOnce.Do(func() {
		file_pbGame_Bag_Bag_proto_rawDescData = protoimpl.X.CompressGZIP(file_pbGame_Bag_Bag_proto_rawDescData)
	})
	return file_pbGame_Bag_Bag_proto_rawDescData
}

var file_pbGame_Bag_Bag_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_pbGame_Bag_Bag_proto_goTypes = []interface{}{
	(ItemUseType)(0),    // 0: proto.Bag.ItemUseType
	(ItemUseSubType)(0), // 1: proto.Bag.ItemUseSubType
}
var file_pbGame_Bag_Bag_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pbGame_Bag_Bag_proto_init() }
func file_pbGame_Bag_Bag_proto_init() {
	if File_pbGame_Bag_Bag_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pbGame_Bag_Bag_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbGame_Bag_Bag_proto_goTypes,
		DependencyIndexes: file_pbGame_Bag_Bag_proto_depIdxs,
		EnumInfos:         file_pbGame_Bag_Bag_proto_enumTypes,
	}.Build()
	File_pbGame_Bag_Bag_proto = out.File
	file_pbGame_Bag_Bag_proto_rawDesc = nil
	file_pbGame_Bag_Bag_proto_goTypes = nil
	file_pbGame_Bag_Bag_proto_depIdxs = nil
}
