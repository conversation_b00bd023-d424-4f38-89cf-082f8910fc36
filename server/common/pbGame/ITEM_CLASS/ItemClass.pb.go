// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v6.30.1
// source: pbGame/ITEM_CLASS/ItemClass.proto

package ITEM_CLASS

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// After are enums.
// 物品分类
type Type int32

const (
	None               Type = 0  //自动添加
	WEAPON             Type = 1  //武器
	ARMOR              Type = 2  //防具
	PET                Type = 3  //宠物
	USE_ITEM           Type = 4  //可使用道具
	QUEST              Type = 5  //材料
	GEM                Type = 6  //宝石
	OTHER              Type = 7  //其他
	BLOOD_BOTTLE       Type = 8  //血瓶
	PET_EQUIP          Type = 9  //宠物装备
	PET_EQUIP_EXP_BOOK Type = 10 //宠物装备经验
	HORSE              Type = 11 //坐骑
	SEAL               Type = 12 //赋灵
	ENERGY_ESSENCE     Type = 13 //能量
	BOX_CHOOSE_ONE     Type = 14 //自选盒
	PET_ADD_LIFE       Type = 15 //宠物寿命石
	LN_STONE           Type = 16 //传送石
)

// Enum value maps for Type.
var (
	Type_name = map[int32]string{
		0:  "None",
		1:  "WEAPON",
		2:  "ARMOR",
		3:  "PET",
		4:  "USE_ITEM",
		5:  "QUEST",
		6:  "GEM",
		7:  "OTHER",
		8:  "BLOOD_BOTTLE",
		9:  "PET_EQUIP",
		10: "PET_EQUIP_EXP_BOOK",
		11: "HORSE",
		12: "SEAL",
		13: "ENERGY_ESSENCE",
		14: "BOX_CHOOSE_ONE",
		15: "PET_ADD_LIFE",
		16: "LN_STONE",
	}
	Type_value = map[string]int32{
		"None":               0,
		"WEAPON":             1,
		"ARMOR":              2,
		"PET":                3,
		"USE_ITEM":           4,
		"QUEST":              5,
		"GEM":                6,
		"OTHER":              7,
		"BLOOD_BOTTLE":       8,
		"PET_EQUIP":          9,
		"PET_EQUIP_EXP_BOOK": 10,
		"HORSE":              11,
		"SEAL":               12,
		"ENERGY_ESSENCE":     13,
		"BOX_CHOOSE_ONE":     14,
		"PET_ADD_LIFE":       15,
		"LN_STONE":           16,
	}
)

func (x Type) Enum() *Type {
	p := new(Type)
	*p = x
	return p
}

func (x Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Type) Descriptor() protoreflect.EnumDescriptor {
	return file_pbGame_ITEM_CLASS_ItemClass_proto_enumTypes[0].Descriptor()
}

func (Type) Type() protoreflect.EnumType {
	return &file_pbGame_ITEM_CLASS_ItemClass_proto_enumTypes[0]
}

func (x Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Type.Descriptor instead.
func (Type) EnumDescriptor() ([]byte, []int) {
	return file_pbGame_ITEM_CLASS_ItemClass_proto_rawDescGZIP(), []int{0}
}

var File_pbGame_ITEM_CLASS_ItemClass_proto protoreflect.FileDescriptor

var file_pbGame_ITEM_CLASS_ItemClass_proto_rawDesc = []byte{
	0x0a, 0x21, 0x70, 0x62, 0x47, 0x61, 0x6d, 0x65, 0x2f, 0x49, 0x54, 0x45, 0x4d, 0x5f, 0x43, 0x4c,
	0x41, 0x53, 0x53, 0x2f, 0x49, 0x74, 0x65, 0x6d, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x10, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x49, 0x54, 0x45, 0x4d, 0x5f,
	0x43, 0x4c, 0x41, 0x53, 0x53, 0x2a, 0xf3, 0x01, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x12, 0x08,
	0x0a, 0x04, 0x4e, 0x6f, 0x6e, 0x65, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x57, 0x45, 0x41, 0x50,
	0x4f, 0x4e, 0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x41, 0x52, 0x4d, 0x4f, 0x52, 0x10, 0x02, 0x12,
	0x07, 0x0a, 0x03, 0x50, 0x45, 0x54, 0x10, 0x03, 0x12, 0x0c, 0x0a, 0x08, 0x55, 0x53, 0x45, 0x5f,
	0x49, 0x54, 0x45, 0x4d, 0x10, 0x04, 0x12, 0x09, 0x0a, 0x05, 0x51, 0x55, 0x45, 0x53, 0x54, 0x10,
	0x05, 0x12, 0x07, 0x0a, 0x03, 0x47, 0x45, 0x4d, 0x10, 0x06, 0x12, 0x09, 0x0a, 0x05, 0x4f, 0x54,
	0x48, 0x45, 0x52, 0x10, 0x07, 0x12, 0x10, 0x0a, 0x0c, 0x42, 0x4c, 0x4f, 0x4f, 0x44, 0x5f, 0x42,
	0x4f, 0x54, 0x54, 0x4c, 0x45, 0x10, 0x08, 0x12, 0x0d, 0x0a, 0x09, 0x50, 0x45, 0x54, 0x5f, 0x45,
	0x51, 0x55, 0x49, 0x50, 0x10, 0x09, 0x12, 0x16, 0x0a, 0x12, 0x50, 0x45, 0x54, 0x5f, 0x45, 0x51,
	0x55, 0x49, 0x50, 0x5f, 0x45, 0x58, 0x50, 0x5f, 0x42, 0x4f, 0x4f, 0x4b, 0x10, 0x0a, 0x12, 0x09,
	0x0a, 0x05, 0x48, 0x4f, 0x52, 0x53, 0x45, 0x10, 0x0b, 0x12, 0x08, 0x0a, 0x04, 0x53, 0x45, 0x41,
	0x4c, 0x10, 0x0c, 0x12, 0x12, 0x0a, 0x0e, 0x45, 0x4e, 0x45, 0x52, 0x47, 0x59, 0x5f, 0x45, 0x53,
	0x53, 0x45, 0x4e, 0x43, 0x45, 0x10, 0x0d, 0x12, 0x12, 0x0a, 0x0e, 0x42, 0x4f, 0x58, 0x5f, 0x43,
	0x48, 0x4f, 0x4f, 0x53, 0x45, 0x5f, 0x4f, 0x4e, 0x45, 0x10, 0x0e, 0x12, 0x10, 0x0a, 0x0c, 0x50,
	0x45, 0x54, 0x5f, 0x41, 0x44, 0x44, 0x5f, 0x4c, 0x49, 0x46, 0x45, 0x10, 0x0f, 0x12, 0x0c, 0x0a,
	0x08, 0x4c, 0x4e, 0x5f, 0x53, 0x54, 0x4f, 0x4e, 0x45, 0x10, 0x10, 0x42, 0x2b, 0x5a, 0x29, 0x77,
	0x6f, 0x72, 0x6c, 0x64, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x62, 0x47, 0x61,
	0x6d, 0x65, 0x2f, 0x49, 0x54, 0x45, 0x4d, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x3b, 0x49, 0x54,
	0x45, 0x4d, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pbGame_ITEM_CLASS_ItemClass_proto_rawDescOnce sync.Once
	file_pbGame_ITEM_CLASS_ItemClass_proto_rawDescData = file_pbGame_ITEM_CLASS_ItemClass_proto_rawDesc
)

func file_pbGame_ITEM_CLASS_ItemClass_proto_rawDescGZIP() []byte {
	file_pbGame_ITEM_CLASS_ItemClass_proto_rawDescOnce.Do(func() {
		file_pbGame_ITEM_CLASS_ItemClass_proto_rawDescData = protoimpl.X.CompressGZIP(file_pbGame_ITEM_CLASS_ItemClass_proto_rawDescData)
	})
	return file_pbGame_ITEM_CLASS_ItemClass_proto_rawDescData
}

var file_pbGame_ITEM_CLASS_ItemClass_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_pbGame_ITEM_CLASS_ItemClass_proto_goTypes = []interface{}{
	(Type)(0), // 0: proto.ITEM_CLASS.Type
}
var file_pbGame_ITEM_CLASS_ItemClass_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pbGame_ITEM_CLASS_ItemClass_proto_init() }
func file_pbGame_ITEM_CLASS_ItemClass_proto_init() {
	if File_pbGame_ITEM_CLASS_ItemClass_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pbGame_ITEM_CLASS_ItemClass_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbGame_ITEM_CLASS_ItemClass_proto_goTypes,
		DependencyIndexes: file_pbGame_ITEM_CLASS_ItemClass_proto_depIdxs,
		EnumInfos:         file_pbGame_ITEM_CLASS_ItemClass_proto_enumTypes,
	}.Build()
	File_pbGame_ITEM_CLASS_ItemClass_proto = out.File
	file_pbGame_ITEM_CLASS_ItemClass_proto_rawDesc = nil
	file_pbGame_ITEM_CLASS_ItemClass_proto_goTypes = nil
	file_pbGame_ITEM_CLASS_ItemClass_proto_depIdxs = nil
}
