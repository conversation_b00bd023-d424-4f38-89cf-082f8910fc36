// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v6.30.1
// source: pbGame/EQUIP_POS/EquipPos.proto

package EQUIP_POS

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// After are enums.
// 装备位置定义
type Type int32

const (
	ARMOR_NECKLACE_POS   Type = 0  //链
	PET_POS              Type = 1  //宠物
	ARMOR_RING_LEFT_POS  Type = 2  //戒左
	ARMOR_FASHION_POS    Type = 3  //时装
	ARMOR_BACK_POS       Type = 4  //背
	ARMOR_CLOTHES_POS    Type = 5  //衣
	ARMOR_HAND_POS       Type = 6  //手
	ARMOR_HEAD_POS       Type = 7  //头
	WEAPON_LEFT_POS      Type = 8  //武-左
	ARMOR_SHOES_POS      Type = 9  //鞋
	WEAPON_RIGHT_POS     Type = 10 //武-右
	ARMOR_WAIST_POS      Type = 11 //腰
	ARMOR_TROUSERS_POS   Type = 12 //裤
	ARMOR_SHOULDER_POS   Type = 13 //肩
	ARMOR_AMULET_POS     Type = 14 //腿
	ARMOR_TRANSPORT_POS  Type = 15 //坐骑
	ARMOR_RING_RIGHT_POS Type = 16 //戒-右
	SPIRIT_POS           Type = 17 //vip
	BLOOD_BOTTLE_POS     Type = 18 //血
)

// Enum value maps for Type.
var (
	Type_name = map[int32]string{
		0:  "ARMOR_NECKLACE_POS",
		1:  "PET_POS",
		2:  "ARMOR_RING_LEFT_POS",
		3:  "ARMOR_FASHION_POS",
		4:  "ARMOR_BACK_POS",
		5:  "ARMOR_CLOTHES_POS",
		6:  "ARMOR_HAND_POS",
		7:  "ARMOR_HEAD_POS",
		8:  "WEAPON_LEFT_POS",
		9:  "ARMOR_SHOES_POS",
		10: "WEAPON_RIGHT_POS",
		11: "ARMOR_WAIST_POS",
		12: "ARMOR_TROUSERS_POS",
		13: "ARMOR_SHOULDER_POS",
		14: "ARMOR_AMULET_POS",
		15: "ARMOR_TRANSPORT_POS",
		16: "ARMOR_RING_RIGHT_POS",
		17: "SPIRIT_POS",
		18: "BLOOD_BOTTLE_POS",
	}
	Type_value = map[string]int32{
		"ARMOR_NECKLACE_POS":   0,
		"PET_POS":              1,
		"ARMOR_RING_LEFT_POS":  2,
		"ARMOR_FASHION_POS":    3,
		"ARMOR_BACK_POS":       4,
		"ARMOR_CLOTHES_POS":    5,
		"ARMOR_HAND_POS":       6,
		"ARMOR_HEAD_POS":       7,
		"WEAPON_LEFT_POS":      8,
		"ARMOR_SHOES_POS":      9,
		"WEAPON_RIGHT_POS":     10,
		"ARMOR_WAIST_POS":      11,
		"ARMOR_TROUSERS_POS":   12,
		"ARMOR_SHOULDER_POS":   13,
		"ARMOR_AMULET_POS":     14,
		"ARMOR_TRANSPORT_POS":  15,
		"ARMOR_RING_RIGHT_POS": 16,
		"SPIRIT_POS":           17,
		"BLOOD_BOTTLE_POS":     18,
	}
)

func (x Type) Enum() *Type {
	p := new(Type)
	*p = x
	return p
}

func (x Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Type) Descriptor() protoreflect.EnumDescriptor {
	return file_pbGame_EQUIP_POS_EquipPos_proto_enumTypes[0].Descriptor()
}

func (Type) Type() protoreflect.EnumType {
	return &file_pbGame_EQUIP_POS_EquipPos_proto_enumTypes[0]
}

func (x Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Type.Descriptor instead.
func (Type) EnumDescriptor() ([]byte, []int) {
	return file_pbGame_EQUIP_POS_EquipPos_proto_rawDescGZIP(), []int{0}
}

var File_pbGame_EQUIP_POS_EquipPos_proto protoreflect.FileDescriptor

var file_pbGame_EQUIP_POS_EquipPos_proto_rawDesc = []byte{
	0x0a, 0x1f, 0x70, 0x62, 0x47, 0x61, 0x6d, 0x65, 0x2f, 0x45, 0x51, 0x55, 0x49, 0x50, 0x5f, 0x50,
	0x4f, 0x53, 0x2f, 0x45, 0x71, 0x75, 0x69, 0x70, 0x50, 0x6f, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x0f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x45, 0x51, 0x55, 0x49, 0x50, 0x5f, 0x50,
	0x4f, 0x53, 0x2a, 0xa2, 0x03, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x12, 0x41,
	0x52, 0x4d, 0x4f, 0x52, 0x5f, 0x4e, 0x45, 0x43, 0x4b, 0x4c, 0x41, 0x43, 0x45, 0x5f, 0x50, 0x4f,
	0x53, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x50, 0x45, 0x54, 0x5f, 0x50, 0x4f, 0x53, 0x10, 0x01,
	0x12, 0x17, 0x0a, 0x13, 0x41, 0x52, 0x4d, 0x4f, 0x52, 0x5f, 0x52, 0x49, 0x4e, 0x47, 0x5f, 0x4c,
	0x45, 0x46, 0x54, 0x5f, 0x50, 0x4f, 0x53, 0x10, 0x02, 0x12, 0x15, 0x0a, 0x11, 0x41, 0x52, 0x4d,
	0x4f, 0x52, 0x5f, 0x46, 0x41, 0x53, 0x48, 0x49, 0x4f, 0x4e, 0x5f, 0x50, 0x4f, 0x53, 0x10, 0x03,
	0x12, 0x12, 0x0a, 0x0e, 0x41, 0x52, 0x4d, 0x4f, 0x52, 0x5f, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x50,
	0x4f, 0x53, 0x10, 0x04, 0x12, 0x15, 0x0a, 0x11, 0x41, 0x52, 0x4d, 0x4f, 0x52, 0x5f, 0x43, 0x4c,
	0x4f, 0x54, 0x48, 0x45, 0x53, 0x5f, 0x50, 0x4f, 0x53, 0x10, 0x05, 0x12, 0x12, 0x0a, 0x0e, 0x41,
	0x52, 0x4d, 0x4f, 0x52, 0x5f, 0x48, 0x41, 0x4e, 0x44, 0x5f, 0x50, 0x4f, 0x53, 0x10, 0x06, 0x12,
	0x12, 0x0a, 0x0e, 0x41, 0x52, 0x4d, 0x4f, 0x52, 0x5f, 0x48, 0x45, 0x41, 0x44, 0x5f, 0x50, 0x4f,
	0x53, 0x10, 0x07, 0x12, 0x13, 0x0a, 0x0f, 0x57, 0x45, 0x41, 0x50, 0x4f, 0x4e, 0x5f, 0x4c, 0x45,
	0x46, 0x54, 0x5f, 0x50, 0x4f, 0x53, 0x10, 0x08, 0x12, 0x13, 0x0a, 0x0f, 0x41, 0x52, 0x4d, 0x4f,
	0x52, 0x5f, 0x53, 0x48, 0x4f, 0x45, 0x53, 0x5f, 0x50, 0x4f, 0x53, 0x10, 0x09, 0x12, 0x14, 0x0a,
	0x10, 0x57, 0x45, 0x41, 0x50, 0x4f, 0x4e, 0x5f, 0x52, 0x49, 0x47, 0x48, 0x54, 0x5f, 0x50, 0x4f,
	0x53, 0x10, 0x0a, 0x12, 0x13, 0x0a, 0x0f, 0x41, 0x52, 0x4d, 0x4f, 0x52, 0x5f, 0x57, 0x41, 0x49,
	0x53, 0x54, 0x5f, 0x50, 0x4f, 0x53, 0x10, 0x0b, 0x12, 0x16, 0x0a, 0x12, 0x41, 0x52, 0x4d, 0x4f,
	0x52, 0x5f, 0x54, 0x52, 0x4f, 0x55, 0x53, 0x45, 0x52, 0x53, 0x5f, 0x50, 0x4f, 0x53, 0x10, 0x0c,
	0x12, 0x16, 0x0a, 0x12, 0x41, 0x52, 0x4d, 0x4f, 0x52, 0x5f, 0x53, 0x48, 0x4f, 0x55, 0x4c, 0x44,
	0x45, 0x52, 0x5f, 0x50, 0x4f, 0x53, 0x10, 0x0d, 0x12, 0x14, 0x0a, 0x10, 0x41, 0x52, 0x4d, 0x4f,
	0x52, 0x5f, 0x41, 0x4d, 0x55, 0x4c, 0x45, 0x54, 0x5f, 0x50, 0x4f, 0x53, 0x10, 0x0e, 0x12, 0x17,
	0x0a, 0x13, 0x41, 0x52, 0x4d, 0x4f, 0x52, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x50, 0x4f, 0x52,
	0x54, 0x5f, 0x50, 0x4f, 0x53, 0x10, 0x0f, 0x12, 0x18, 0x0a, 0x14, 0x41, 0x52, 0x4d, 0x4f, 0x52,
	0x5f, 0x52, 0x49, 0x4e, 0x47, 0x5f, 0x52, 0x49, 0x47, 0x48, 0x54, 0x5f, 0x50, 0x4f, 0x53, 0x10,
	0x10, 0x12, 0x0e, 0x0a, 0x0a, 0x53, 0x50, 0x49, 0x52, 0x49, 0x54, 0x5f, 0x50, 0x4f, 0x53, 0x10,
	0x11, 0x12, 0x14, 0x0a, 0x10, 0x42, 0x4c, 0x4f, 0x4f, 0x44, 0x5f, 0x42, 0x4f, 0x54, 0x54, 0x4c,
	0x45, 0x5f, 0x50, 0x4f, 0x53, 0x10, 0x12, 0x42, 0x29, 0x5a, 0x27, 0x77, 0x6f, 0x72, 0x6c, 0x64,
	0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x62, 0x47, 0x61, 0x6d, 0x65, 0x2f, 0x45,
	0x51, 0x55, 0x49, 0x50, 0x5f, 0x50, 0x4f, 0x53, 0x3b, 0x45, 0x51, 0x55, 0x49, 0x50, 0x5f, 0x50,
	0x4f, 0x53, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pbGame_EQUIP_POS_EquipPos_proto_rawDescOnce sync.Once
	file_pbGame_EQUIP_POS_EquipPos_proto_rawDescData = file_pbGame_EQUIP_POS_EquipPos_proto_rawDesc
)

func file_pbGame_EQUIP_POS_EquipPos_proto_rawDescGZIP() []byte {
	file_pbGame_EQUIP_POS_EquipPos_proto_rawDescOnce.Do(func() {
		file_pbGame_EQUIP_POS_EquipPos_proto_rawDescData = protoimpl.X.CompressGZIP(file_pbGame_EQUIP_POS_EquipPos_proto_rawDescData)
	})
	return file_pbGame_EQUIP_POS_EquipPos_proto_rawDescData
}

var file_pbGame_EQUIP_POS_EquipPos_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_pbGame_EQUIP_POS_EquipPos_proto_goTypes = []interface{}{
	(Type)(0), // 0: proto.EQUIP_POS.Type
}
var file_pbGame_EQUIP_POS_EquipPos_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pbGame_EQUIP_POS_EquipPos_proto_init() }
func file_pbGame_EQUIP_POS_EquipPos_proto_init() {
	if File_pbGame_EQUIP_POS_EquipPos_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pbGame_EQUIP_POS_EquipPos_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbGame_EQUIP_POS_EquipPos_proto_goTypes,
		DependencyIndexes: file_pbGame_EQUIP_POS_EquipPos_proto_depIdxs,
		EnumInfos:         file_pbGame_EQUIP_POS_EquipPos_proto_enumTypes,
	}.Build()
	File_pbGame_EQUIP_POS_EquipPos_proto = out.File
	file_pbGame_EQUIP_POS_EquipPos_proto_rawDesc = nil
	file_pbGame_EQUIP_POS_EquipPos_proto_goTypes = nil
	file_pbGame_EQUIP_POS_EquipPos_proto_depIdxs = nil
}
