// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v6.30.1
// source: pbGame/gameMap.proto

package pbGame

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	Response "world/common/pbBase/Response"
	pbCross "world/common/pbCross"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// After are messages.
type S2C_EnterMapMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Plr []*pbCross.CrossSimplePlayer `protobuf:"bytes,1,rep,name=plr,proto3" json:"plr,omitempty"` //玩家简要数据
}

func (x *S2C_EnterMapMessage) Reset() {
	*x = S2C_EnterMapMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_gameMap_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_EnterMapMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_EnterMapMessage) ProtoMessage() {}

func (x *S2C_EnterMapMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_gameMap_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_EnterMapMessage.ProtoReflect.Descriptor instead.
func (*S2C_EnterMapMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_gameMap_proto_rawDescGZIP(), []int{0}
}

func (x *S2C_EnterMapMessage) GetPlr() []*pbCross.CrossSimplePlayer {
	if x != nil {
		return x.Plr
	}
	return nil
}

type S2C_LeaveMapMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id []int32 `protobuf:"varint,1,rep,packed,name=id,proto3" json:"id,omitempty"` //玩家id
}

func (x *S2C_LeaveMapMessage) Reset() {
	*x = S2C_LeaveMapMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_gameMap_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_LeaveMapMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_LeaveMapMessage) ProtoMessage() {}

func (x *S2C_LeaveMapMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_gameMap_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_LeaveMapMessage.ProtoReflect.Descriptor instead.
func (*S2C_LeaveMapMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_gameMap_proto_rawDescGZIP(), []int{1}
}

func (x *S2C_LeaveMapMessage) GetId() []int32 {
	if x != nil {
		return x.Id
	}
	return nil
}

type S2C_OtherMoveMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`     //玩家id
	X    int32 `protobuf:"varint,2,opt,name=x,proto3" json:"x,omitempty"`       //x
	Y    int32 `protobuf:"varint,3,opt,name=y,proto3" json:"y,omitempty"`       //y
	Mode int32 `protobuf:"varint,4,opt,name=mode,proto3" json:"mode,omitempty"` //mode
}

func (x *S2C_OtherMoveMessage) Reset() {
	*x = S2C_OtherMoveMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_gameMap_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_OtherMoveMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_OtherMoveMessage) ProtoMessage() {}

func (x *S2C_OtherMoveMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_gameMap_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_OtherMoveMessage.ProtoReflect.Descriptor instead.
func (*S2C_OtherMoveMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_gameMap_proto_rawDescGZIP(), []int{2}
}

func (x *S2C_OtherMoveMessage) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *S2C_OtherMoveMessage) GetX() int32 {
	if x != nil {
		return x.X
	}
	return 0
}

func (x *S2C_OtherMoveMessage) GetY() int32 {
	if x != nil {
		return x.Y
	}
	return 0
}

func (x *S2C_OtherMoveMessage) GetMode() int32 {
	if x != nil {
		return x.Mode
	}
	return 0
}

type C2S_TeamJoinMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OtherId int32 `protobuf:"varint,1,opt,name=otherId,proto3" json:"otherId,omitempty"` //其他玩家id
}

func (x *C2S_TeamJoinMessage) Reset() {
	*x = C2S_TeamJoinMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_gameMap_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_TeamJoinMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_TeamJoinMessage) ProtoMessage() {}

func (x *C2S_TeamJoinMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_gameMap_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_TeamJoinMessage.ProtoReflect.Descriptor instead.
func (*C2S_TeamJoinMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_gameMap_proto_rawDescGZIP(), []int{3}
}

func (x *C2S_TeamJoinMessage) GetOtherId() int32 {
	if x != nil {
		return x.OtherId
	}
	return 0
}

type S2C_TeamJoinMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code Response.Code `protobuf:"varint,1,opt,name=code,proto3,enum=proto.Response.Code" json:"code,omitempty"` //响应码
}

func (x *S2C_TeamJoinMessage) Reset() {
	*x = S2C_TeamJoinMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_gameMap_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_TeamJoinMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_TeamJoinMessage) ProtoMessage() {}

func (x *S2C_TeamJoinMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_gameMap_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_TeamJoinMessage.ProtoReflect.Descriptor instead.
func (*S2C_TeamJoinMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_gameMap_proto_rawDescGZIP(), []int{4}
}

func (x *S2C_TeamJoinMessage) GetCode() Response.Code {
	if x != nil {
		return x.Code
	}
	return Response.Code(0)
}

type C2S_TeamInviteMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OtherId int32 `protobuf:"varint,1,opt,name=otherId,proto3" json:"otherId,omitempty"` //其他玩家id
}

func (x *C2S_TeamInviteMessage) Reset() {
	*x = C2S_TeamInviteMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_gameMap_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_TeamInviteMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_TeamInviteMessage) ProtoMessage() {}

func (x *C2S_TeamInviteMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_gameMap_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_TeamInviteMessage.ProtoReflect.Descriptor instead.
func (*C2S_TeamInviteMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_gameMap_proto_rawDescGZIP(), []int{5}
}

func (x *C2S_TeamInviteMessage) GetOtherId() int32 {
	if x != nil {
		return x.OtherId
	}
	return 0
}

type S2C_TeamInviteMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code Response.Code `protobuf:"varint,1,opt,name=code,proto3,enum=proto.Response.Code" json:"code,omitempty"` //响应码
}

func (x *S2C_TeamInviteMessage) Reset() {
	*x = S2C_TeamInviteMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_gameMap_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_TeamInviteMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_TeamInviteMessage) ProtoMessage() {}

func (x *S2C_TeamInviteMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_gameMap_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_TeamInviteMessage.ProtoReflect.Descriptor instead.
func (*S2C_TeamInviteMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_gameMap_proto_rawDescGZIP(), []int{6}
}

func (x *S2C_TeamInviteMessage) GetCode() Response.Code {
	if x != nil {
		return x.Code
	}
	return Response.Code(0)
}

type C2S_JumpMapMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ToMapId int32 `protobuf:"varint,1,opt,name=toMapId,proto3" json:"toMapId,omitempty"` //目标地图id
	ToMapX  int32 `protobuf:"varint,2,opt,name=toMapX,proto3" json:"toMapX,omitempty"`   //x
	ToMapY  int32 `protobuf:"varint,3,opt,name=toMapY,proto3" json:"toMapY,omitempty"`   //y
}

func (x *C2S_JumpMapMessage) Reset() {
	*x = C2S_JumpMapMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_gameMap_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_JumpMapMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_JumpMapMessage) ProtoMessage() {}

func (x *C2S_JumpMapMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_gameMap_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_JumpMapMessage.ProtoReflect.Descriptor instead.
func (*C2S_JumpMapMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_gameMap_proto_rawDescGZIP(), []int{7}
}

func (x *C2S_JumpMapMessage) GetToMapId() int32 {
	if x != nil {
		return x.ToMapId
	}
	return 0
}

func (x *C2S_JumpMapMessage) GetToMapX() int32 {
	if x != nil {
		return x.ToMapX
	}
	return 0
}

func (x *C2S_JumpMapMessage) GetToMapY() int32 {
	if x != nil {
		return x.ToMapY
	}
	return 0
}

type S2C_JumpMapMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code Response.Code `protobuf:"varint,1,opt,name=code,proto3,enum=proto.Response.Code" json:"code,omitempty"` //响应码
}

func (x *S2C_JumpMapMessage) Reset() {
	*x = S2C_JumpMapMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_gameMap_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_JumpMapMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_JumpMapMessage) ProtoMessage() {}

func (x *S2C_JumpMapMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_gameMap_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_JumpMapMessage.ProtoReflect.Descriptor instead.
func (*S2C_JumpMapMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_gameMap_proto_rawDescGZIP(), []int{8}
}

func (x *S2C_JumpMapMessage) GetCode() Response.Code {
	if x != nil {
		return x.Code
	}
	return Response.Code(0)
}

type C2S_PlayerMoveMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pos *Point `protobuf:"bytes,1,opt,name=pos,proto3" json:"pos,omitempty"` //位置信息
}

func (x *C2S_PlayerMoveMessage) Reset() {
	*x = C2S_PlayerMoveMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_gameMap_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_PlayerMoveMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_PlayerMoveMessage) ProtoMessage() {}

func (x *C2S_PlayerMoveMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_gameMap_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_PlayerMoveMessage.ProtoReflect.Descriptor instead.
func (*C2S_PlayerMoveMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_gameMap_proto_rawDescGZIP(), []int{9}
}

func (x *C2S_PlayerMoveMessage) GetPos() *Point {
	if x != nil {
		return x.Pos
	}
	return nil
}

type S2C_PlayerMoveMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code Response.Code `protobuf:"varint,1,opt,name=code,proto3,enum=proto.Response.Code" json:"code,omitempty"` //
}

func (x *S2C_PlayerMoveMessage) Reset() {
	*x = S2C_PlayerMoveMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_gameMap_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_PlayerMoveMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_PlayerMoveMessage) ProtoMessage() {}

func (x *S2C_PlayerMoveMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_gameMap_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_PlayerMoveMessage.ProtoReflect.Descriptor instead.
func (*S2C_PlayerMoveMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_gameMap_proto_rawDescGZIP(), []int{10}
}

func (x *S2C_PlayerMoveMessage) GetCode() Response.Code {
	if x != nil {
		return x.Code
	}
	return Response.Code(0)
}

type S2C_ScenePlayerEventMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Event *pbCross.PlayerEvent `protobuf:"bytes,1,opt,name=event,proto3" json:"event,omitempty"` //
}

func (x *S2C_ScenePlayerEventMessage) Reset() {
	*x = S2C_ScenePlayerEventMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_gameMap_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_ScenePlayerEventMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_ScenePlayerEventMessage) ProtoMessage() {}

func (x *S2C_ScenePlayerEventMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_gameMap_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_ScenePlayerEventMessage.ProtoReflect.Descriptor instead.
func (*S2C_ScenePlayerEventMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_gameMap_proto_rawDescGZIP(), []int{11}
}

func (x *S2C_ScenePlayerEventMessage) GetEvent() *pbCross.PlayerEvent {
	if x != nil {
		return x.Event
	}
	return nil
}

type C2S_PlayerEventChooseMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id  int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`   //事件id
	Yes bool  `protobuf:"varint,2,opt,name=yes,proto3" json:"yes,omitempty"` //是/否
}

func (x *C2S_PlayerEventChooseMessage) Reset() {
	*x = C2S_PlayerEventChooseMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_gameMap_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_PlayerEventChooseMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_PlayerEventChooseMessage) ProtoMessage() {}

func (x *C2S_PlayerEventChooseMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_gameMap_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_PlayerEventChooseMessage.ProtoReflect.Descriptor instead.
func (*C2S_PlayerEventChooseMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_gameMap_proto_rawDescGZIP(), []int{12}
}

func (x *C2S_PlayerEventChooseMessage) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *C2S_PlayerEventChooseMessage) GetYes() bool {
	if x != nil {
		return x.Yes
	}
	return false
}

type S2C_PlayerEventChooseMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code Response.Code `protobuf:"varint,1,opt,name=code,proto3,enum=proto.Response.Code" json:"code,omitempty"` //响应码
}

func (x *S2C_PlayerEventChooseMessage) Reset() {
	*x = S2C_PlayerEventChooseMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_gameMap_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_PlayerEventChooseMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_PlayerEventChooseMessage) ProtoMessage() {}

func (x *S2C_PlayerEventChooseMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_gameMap_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_PlayerEventChooseMessage.ProtoReflect.Descriptor instead.
func (*S2C_PlayerEventChooseMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_gameMap_proto_rawDescGZIP(), []int{13}
}

func (x *S2C_PlayerEventChooseMessage) GetCode() Response.Code {
	if x != nil {
		return x.Code
	}
	return Response.Code(0)
}

type S2C_GetPlayerEventChooseResultMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EventType int32                      `protobuf:"varint,1,opt,name=eventType,proto3" json:"eventType,omitempty"` //事件类型
	Yes       bool                       `protobuf:"varint,2,opt,name=yes,proto3" json:"yes,omitempty"`             //选择结果是/否
	Plr       *pbCross.CrossSimplePlayer `protobuf:"bytes,3,opt,name=plr,proto3" json:"plr,omitempty"`              //玩家简要数据
}

func (x *S2C_GetPlayerEventChooseResultMessage) Reset() {
	*x = S2C_GetPlayerEventChooseResultMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_gameMap_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_GetPlayerEventChooseResultMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_GetPlayerEventChooseResultMessage) ProtoMessage() {}

func (x *S2C_GetPlayerEventChooseResultMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_gameMap_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_GetPlayerEventChooseResultMessage.ProtoReflect.Descriptor instead.
func (*S2C_GetPlayerEventChooseResultMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_gameMap_proto_rawDescGZIP(), []int{14}
}

func (x *S2C_GetPlayerEventChooseResultMessage) GetEventType() int32 {
	if x != nil {
		return x.EventType
	}
	return 0
}

func (x *S2C_GetPlayerEventChooseResultMessage) GetYes() bool {
	if x != nil {
		return x.Yes
	}
	return false
}

func (x *S2C_GetPlayerEventChooseResultMessage) GetPlr() *pbCross.CrossSimplePlayer {
	if x != nil {
		return x.Plr
	}
	return nil
}

type S2C_BroadcastTeamJoinMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Member int32 `protobuf:"varint,1,opt,name=member,proto3" json:"member,omitempty"` //队员id
	Leader int32 `protobuf:"varint,2,opt,name=leader,proto3" json:"leader,omitempty"` //队长id
}

func (x *S2C_BroadcastTeamJoinMessage) Reset() {
	*x = S2C_BroadcastTeamJoinMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_gameMap_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_BroadcastTeamJoinMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_BroadcastTeamJoinMessage) ProtoMessage() {}

func (x *S2C_BroadcastTeamJoinMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_gameMap_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_BroadcastTeamJoinMessage.ProtoReflect.Descriptor instead.
func (*S2C_BroadcastTeamJoinMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_gameMap_proto_rawDescGZIP(), []int{15}
}

func (x *S2C_BroadcastTeamJoinMessage) GetMember() int32 {
	if x != nil {
		return x.Member
	}
	return 0
}

func (x *S2C_BroadcastTeamJoinMessage) GetLeader() int32 {
	if x != nil {
		return x.Leader
	}
	return 0
}

type S2C_BroadcastTeamLeaveMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Member int32 `protobuf:"varint,1,opt,name=member,proto3" json:"member,omitempty"` //队员id
	Leader int32 `protobuf:"varint,2,opt,name=leader,proto3" json:"leader,omitempty"` //队长id
}

func (x *S2C_BroadcastTeamLeaveMessage) Reset() {
	*x = S2C_BroadcastTeamLeaveMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_gameMap_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_BroadcastTeamLeaveMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_BroadcastTeamLeaveMessage) ProtoMessage() {}

func (x *S2C_BroadcastTeamLeaveMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_gameMap_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_BroadcastTeamLeaveMessage.ProtoReflect.Descriptor instead.
func (*S2C_BroadcastTeamLeaveMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_gameMap_proto_rawDescGZIP(), []int{16}
}

func (x *S2C_BroadcastTeamLeaveMessage) GetMember() int32 {
	if x != nil {
		return x.Member
	}
	return 0
}

func (x *S2C_BroadcastTeamLeaveMessage) GetLeader() int32 {
	if x != nil {
		return x.Leader
	}
	return 0
}

type C2S_ExitTeamMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *C2S_ExitTeamMessage) Reset() {
	*x = C2S_ExitTeamMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_gameMap_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_ExitTeamMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_ExitTeamMessage) ProtoMessage() {}

func (x *C2S_ExitTeamMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_gameMap_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_ExitTeamMessage.ProtoReflect.Descriptor instead.
func (*C2S_ExitTeamMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_gameMap_proto_rawDescGZIP(), []int{17}
}

type S2C_ExitTeamMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code Response.Code `protobuf:"varint,1,opt,name=code,proto3,enum=proto.Response.Code" json:"code,omitempty"` //响应码
}

func (x *S2C_ExitTeamMessage) Reset() {
	*x = S2C_ExitTeamMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_gameMap_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_ExitTeamMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_ExitTeamMessage) ProtoMessage() {}

func (x *S2C_ExitTeamMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_gameMap_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_ExitTeamMessage.ProtoReflect.Descriptor instead.
func (*S2C_ExitTeamMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_gameMap_proto_rawDescGZIP(), []int{18}
}

func (x *S2C_ExitTeamMessage) GetCode() Response.Code {
	if x != nil {
		return x.Code
	}
	return Response.Code(0)
}

type C2S_RemoveMemberMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MemberId int32 `protobuf:"varint,1,opt,name=memberId,proto3" json:"memberId,omitempty"` //队员id
}

func (x *C2S_RemoveMemberMessage) Reset() {
	*x = C2S_RemoveMemberMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_gameMap_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_RemoveMemberMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_RemoveMemberMessage) ProtoMessage() {}

func (x *C2S_RemoveMemberMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_gameMap_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_RemoveMemberMessage.ProtoReflect.Descriptor instead.
func (*C2S_RemoveMemberMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_gameMap_proto_rawDescGZIP(), []int{19}
}

func (x *C2S_RemoveMemberMessage) GetMemberId() int32 {
	if x != nil {
		return x.MemberId
	}
	return 0
}

type S2C_RemoveMemberMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code Response.Code `protobuf:"varint,1,opt,name=code,proto3,enum=proto.Response.Code" json:"code,omitempty"` //响应码
}

func (x *S2C_RemoveMemberMessage) Reset() {
	*x = S2C_RemoveMemberMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_gameMap_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_RemoveMemberMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_RemoveMemberMessage) ProtoMessage() {}

func (x *S2C_RemoveMemberMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_gameMap_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_RemoveMemberMessage.ProtoReflect.Descriptor instead.
func (*S2C_RemoveMemberMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_gameMap_proto_rawDescGZIP(), []int{20}
}

func (x *S2C_RemoveMemberMessage) GetCode() Response.Code {
	if x != nil {
		return x.Code
	}
	return Response.Code(0)
}

type C2S_ChangeLeaderMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MemberId int32 `protobuf:"varint,1,opt,name=memberId,proto3" json:"memberId,omitempty"` //队员id
}

func (x *C2S_ChangeLeaderMessage) Reset() {
	*x = C2S_ChangeLeaderMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_gameMap_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_ChangeLeaderMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_ChangeLeaderMessage) ProtoMessage() {}

func (x *C2S_ChangeLeaderMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_gameMap_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_ChangeLeaderMessage.ProtoReflect.Descriptor instead.
func (*C2S_ChangeLeaderMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_gameMap_proto_rawDescGZIP(), []int{21}
}

func (x *C2S_ChangeLeaderMessage) GetMemberId() int32 {
	if x != nil {
		return x.MemberId
	}
	return 0
}

type S2C_ChangeLeaderMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code Response.Code `protobuf:"varint,1,opt,name=code,proto3,enum=proto.Response.Code" json:"code,omitempty"` //响应码
}

func (x *S2C_ChangeLeaderMessage) Reset() {
	*x = S2C_ChangeLeaderMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_gameMap_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_ChangeLeaderMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_ChangeLeaderMessage) ProtoMessage() {}

func (x *S2C_ChangeLeaderMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_gameMap_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_ChangeLeaderMessage.ProtoReflect.Descriptor instead.
func (*S2C_ChangeLeaderMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_gameMap_proto_rawDescGZIP(), []int{22}
}

func (x *S2C_ChangeLeaderMessage) GetCode() Response.Code {
	if x != nil {
		return x.Code
	}
	return Response.Code(0)
}

type S2C_BroadcastChangeLeaderMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OldLeader int32   `protobuf:"varint,1,opt,name=oldLeader,proto3" json:"oldLeader,omitempty"`    //原队长id
	NewLeader int32   `protobuf:"varint,2,opt,name=newLeader,proto3" json:"newLeader,omitempty"`    //新队长id
	Members   []int32 `protobuf:"varint,3,rep,packed,name=members,proto3" json:"members,omitempty"` //新队员列表
}

func (x *S2C_BroadcastChangeLeaderMessage) Reset() {
	*x = S2C_BroadcastChangeLeaderMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_gameMap_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_BroadcastChangeLeaderMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_BroadcastChangeLeaderMessage) ProtoMessage() {}

func (x *S2C_BroadcastChangeLeaderMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_gameMap_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_BroadcastChangeLeaderMessage.ProtoReflect.Descriptor instead.
func (*S2C_BroadcastChangeLeaderMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_gameMap_proto_rawDescGZIP(), []int{23}
}

func (x *S2C_BroadcastChangeLeaderMessage) GetOldLeader() int32 {
	if x != nil {
		return x.OldLeader
	}
	return 0
}

func (x *S2C_BroadcastChangeLeaderMessage) GetNewLeader() int32 {
	if x != nil {
		return x.NewLeader
	}
	return 0
}

func (x *S2C_BroadcastChangeLeaderMessage) GetMembers() []int32 {
	if x != nil {
		return x.Members
	}
	return nil
}

type C2S_DisbandTeamMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *C2S_DisbandTeamMessage) Reset() {
	*x = C2S_DisbandTeamMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_gameMap_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_DisbandTeamMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_DisbandTeamMessage) ProtoMessage() {}

func (x *C2S_DisbandTeamMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_gameMap_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_DisbandTeamMessage.ProtoReflect.Descriptor instead.
func (*C2S_DisbandTeamMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_gameMap_proto_rawDescGZIP(), []int{24}
}

type S2C_DisbandTeamMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code Response.Code `protobuf:"varint,1,opt,name=code,proto3,enum=proto.Response.Code" json:"code,omitempty"` //响应码
}

func (x *S2C_DisbandTeamMessage) Reset() {
	*x = S2C_DisbandTeamMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_gameMap_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_DisbandTeamMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_DisbandTeamMessage) ProtoMessage() {}

func (x *S2C_DisbandTeamMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_gameMap_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_DisbandTeamMessage.ProtoReflect.Descriptor instead.
func (*S2C_DisbandTeamMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_gameMap_proto_rawDescGZIP(), []int{25}
}

func (x *S2C_DisbandTeamMessage) GetCode() Response.Code {
	if x != nil {
		return x.Code
	}
	return Response.Code(0)
}

type S2C_BroadcastDisbandTeamMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Leader int32 `protobuf:"varint,1,opt,name=leader,proto3" json:"leader,omitempty"` //原队长id
}

func (x *S2C_BroadcastDisbandTeamMessage) Reset() {
	*x = S2C_BroadcastDisbandTeamMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_gameMap_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_BroadcastDisbandTeamMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_BroadcastDisbandTeamMessage) ProtoMessage() {}

func (x *S2C_BroadcastDisbandTeamMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_gameMap_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_BroadcastDisbandTeamMessage.ProtoReflect.Descriptor instead.
func (*S2C_BroadcastDisbandTeamMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_gameMap_proto_rawDescGZIP(), []int{26}
}

func (x *S2C_BroadcastDisbandTeamMessage) GetLeader() int32 {
	if x != nil {
		return x.Leader
	}
	return 0
}

var File_pbGame_gameMap_proto protoreflect.FileDescriptor

var file_pbGame_gameMap_proto_rawDesc = []byte{
	0x0a, 0x14, 0x70, 0x62, 0x47, 0x61, 0x6d, 0x65, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x4d, 0x61, 0x70,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x70,
	0x62, 0x43, 0x72, 0x6f, 0x73, 0x73, 0x2f, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x70, 0x62, 0x42, 0x61, 0x73, 0x65, 0x2f, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x2f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x13, 0x70, 0x62, 0x47, 0x61, 0x6d, 0x65, 0x2f, 0x73, 0x74, 0x72, 0x75,
	0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x41, 0x0a, 0x13, 0x53, 0x32, 0x43, 0x5f,
	0x45, 0x6e, 0x74, 0x65, 0x72, 0x4d, 0x61, 0x70, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12,
	0x2a, 0x0a, 0x03, 0x70, 0x6c, 0x72, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x72, 0x6f, 0x73, 0x73, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65,
	0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x52, 0x03, 0x70, 0x6c, 0x72, 0x22, 0x25, 0x0a, 0x13, 0x53,
	0x32, 0x43, 0x5f, 0x4c, 0x65, 0x61, 0x76, 0x65, 0x4d, 0x61, 0x70, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x05, 0x52, 0x02,
	0x69, 0x64, 0x22, 0x56, 0x0a, 0x14, 0x53, 0x32, 0x43, 0x5f, 0x4f, 0x74, 0x68, 0x65, 0x72, 0x4d,
	0x6f, 0x76, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x0c, 0x0a, 0x01, 0x78, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x78, 0x12, 0x0c, 0x0a, 0x01, 0x79, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x01, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x6d, 0x6f, 0x64, 0x65, 0x22, 0x2f, 0x0a, 0x13, 0x43, 0x32,
	0x53, 0x5f, 0x54, 0x65, 0x61, 0x6d, 0x4a, 0x6f, 0x69, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x12, 0x18, 0x0a, 0x07, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x07, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x49, 0x64, 0x22, 0x3f, 0x0a, 0x13, 0x53,
	0x32, 0x43, 0x5f, 0x54, 0x65, 0x61, 0x6d, 0x4a, 0x6f, 0x69, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x2e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x31, 0x0a, 0x15,
	0x43, 0x32, 0x53, 0x5f, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x49, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x49, 0x64, 0x22,
	0x41, 0x0a, 0x15, 0x53, 0x32, 0x43, 0x5f, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x76, 0x69, 0x74,
	0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x22, 0x5e, 0x0a, 0x12, 0x43, 0x32, 0x53, 0x5f, 0x4a, 0x75, 0x6d, 0x70, 0x4d, 0x61,
	0x70, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x74, 0x6f, 0x4d, 0x61,
	0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x74, 0x6f, 0x4d, 0x61, 0x70,
	0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x6f, 0x4d, 0x61, 0x70, 0x58, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x06, 0x74, 0x6f, 0x4d, 0x61, 0x70, 0x58, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x6f,
	0x4d, 0x61, 0x70, 0x59, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x74, 0x6f, 0x4d, 0x61,
	0x70, 0x59, 0x22, 0x3e, 0x0a, 0x12, 0x53, 0x32, 0x43, 0x5f, 0x4a, 0x75, 0x6d, 0x70, 0x4d, 0x61,
	0x70, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x22, 0x37, 0x0a, 0x15, 0x43, 0x32, 0x53, 0x5f, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72,
	0x4d, 0x6f, 0x76, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1e, 0x0a, 0x03, 0x70,
	0x6f, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x03, 0x70, 0x6f, 0x73, 0x22, 0x41, 0x0a, 0x15, 0x53,
	0x32, 0x43, 0x5f, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x4d, 0x6f, 0x76, 0x65, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x47,
	0x0a, 0x1b, 0x53, 0x32, 0x43, 0x5f, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x50, 0x6c, 0x61, 0x79, 0x65,
	0x72, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x28, 0x0a,
	0x05, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x45, 0x76, 0x65, 0x6e, 0x74,
	0x52, 0x05, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x22, 0x40, 0x0a, 0x1c, 0x43, 0x32, 0x53, 0x5f, 0x50,
	0x6c, 0x61, 0x79, 0x65, 0x72, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x43, 0x68, 0x6f, 0x6f, 0x73, 0x65,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x79, 0x65, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x03, 0x79, 0x65, 0x73, 0x22, 0x48, 0x0a, 0x1c, 0x53, 0x32, 0x43,
	0x5f, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x43, 0x68, 0x6f, 0x6f,
	0x73, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x22, 0x83, 0x01, 0x0a, 0x25, 0x53, 0x32, 0x43, 0x5f, 0x47, 0x65, 0x74, 0x50,
	0x6c, 0x61, 0x79, 0x65, 0x72, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x43, 0x68, 0x6f, 0x6f, 0x73, 0x65,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1c, 0x0a,
	0x09, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x09, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x79,
	0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x03, 0x79, 0x65, 0x73, 0x12, 0x2a, 0x0a,
	0x03, 0x70, 0x6c, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x43, 0x72, 0x6f, 0x73, 0x73, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x50, 0x6c,
	0x61, 0x79, 0x65, 0x72, 0x52, 0x03, 0x70, 0x6c, 0x72, 0x22, 0x4e, 0x0a, 0x1c, 0x53, 0x32, 0x43,
	0x5f, 0x42, 0x72, 0x6f, 0x61, 0x64, 0x63, 0x61, 0x73, 0x74, 0x54, 0x65, 0x61, 0x6d, 0x4a, 0x6f,
	0x69, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x65, 0x6d,
	0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6d, 0x65, 0x6d, 0x62, 0x65,
	0x72, 0x12, 0x16, 0x0a, 0x06, 0x6c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x06, 0x6c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x4f, 0x0a, 0x1d, 0x53, 0x32, 0x43,
	0x5f, 0x42, 0x72, 0x6f, 0x61, 0x64, 0x63, 0x61, 0x73, 0x74, 0x54, 0x65, 0x61, 0x6d, 0x4c, 0x65,
	0x61, 0x76, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x65,
	0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6d, 0x65, 0x6d, 0x62,
	0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x6c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x06, 0x6c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x15, 0x0a, 0x13, 0x43, 0x32,
	0x53, 0x5f, 0x45, 0x78, 0x69, 0x74, 0x54, 0x65, 0x61, 0x6d, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x22, 0x3f, 0x0a, 0x13, 0x53, 0x32, 0x43, 0x5f, 0x45, 0x78, 0x69, 0x74, 0x54, 0x65, 0x61,
	0x6d, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x22, 0x35, 0x0a, 0x17, 0x43, 0x32, 0x53, 0x5f, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65,
	0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x08, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x49, 0x64, 0x22, 0x43, 0x0a, 0x17, 0x53, 0x32, 0x43,
	0x5f, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x35,
	0x0a, 0x17, 0x43, 0x32, 0x53, 0x5f, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x4c, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6d, 0x65, 0x6d,
	0x62, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x6d, 0x65, 0x6d,
	0x62, 0x65, 0x72, 0x49, 0x64, 0x22, 0x43, 0x0a, 0x17, 0x53, 0x32, 0x43, 0x5f, 0x43, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x4c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e,
	0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x78, 0x0a, 0x20, 0x53, 0x32,
	0x43, 0x5f, 0x42, 0x72, 0x6f, 0x61, 0x64, 0x63, 0x61, 0x73, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x4c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1c,
	0x0a, 0x09, 0x6f, 0x6c, 0x64, 0x4c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x09, 0x6f, 0x6c, 0x64, 0x4c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x1c, 0x0a, 0x09,
	0x6e, 0x65, 0x77, 0x4c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x09, 0x6e, 0x65, 0x77, 0x4c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65,
	0x6d, 0x62, 0x65, 0x72, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x05, 0x52, 0x07, 0x6d, 0x65, 0x6d,
	0x62, 0x65, 0x72, 0x73, 0x22, 0x18, 0x0a, 0x16, 0x43, 0x32, 0x53, 0x5f, 0x44, 0x69, 0x73, 0x62,
	0x61, 0x6e, 0x64, 0x54, 0x65, 0x61, 0x6d, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x42,
	0x0a, 0x16, 0x53, 0x32, 0x43, 0x5f, 0x44, 0x69, 0x73, 0x62, 0x61, 0x6e, 0x64, 0x54, 0x65, 0x61,
	0x6d, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x22, 0x39, 0x0a, 0x1f, 0x53, 0x32, 0x43, 0x5f, 0x42, 0x72, 0x6f, 0x61, 0x64, 0x63,
	0x61, 0x73, 0x74, 0x44, 0x69, 0x73, 0x62, 0x61, 0x6e, 0x64, 0x54, 0x65, 0x61, 0x6d, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x1c, 0x5a,
	0x1a, 0x77, 0x6f, 0x72, 0x6c, 0x64, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x62,
	0x47, 0x61, 0x6d, 0x65, 0x3b, 0x70, 0x62, 0x47, 0x61, 0x6d, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_pbGame_gameMap_proto_rawDescOnce sync.Once
	file_pbGame_gameMap_proto_rawDescData = file_pbGame_gameMap_proto_rawDesc
)

func file_pbGame_gameMap_proto_rawDescGZIP() []byte {
	file_pbGame_gameMap_proto_rawDescOnce.Do(func() {
		file_pbGame_gameMap_proto_rawDescData = protoimpl.X.CompressGZIP(file_pbGame_gameMap_proto_rawDescData)
	})
	return file_pbGame_gameMap_proto_rawDescData
}

var file_pbGame_gameMap_proto_msgTypes = make([]protoimpl.MessageInfo, 27)
var file_pbGame_gameMap_proto_goTypes = []interface{}{
	(*S2C_EnterMapMessage)(nil),                   // 0: proto.S2C_EnterMapMessage
	(*S2C_LeaveMapMessage)(nil),                   // 1: proto.S2C_LeaveMapMessage
	(*S2C_OtherMoveMessage)(nil),                  // 2: proto.S2C_OtherMoveMessage
	(*C2S_TeamJoinMessage)(nil),                   // 3: proto.C2S_TeamJoinMessage
	(*S2C_TeamJoinMessage)(nil),                   // 4: proto.S2C_TeamJoinMessage
	(*C2S_TeamInviteMessage)(nil),                 // 5: proto.C2S_TeamInviteMessage
	(*S2C_TeamInviteMessage)(nil),                 // 6: proto.S2C_TeamInviteMessage
	(*C2S_JumpMapMessage)(nil),                    // 7: proto.C2S_JumpMapMessage
	(*S2C_JumpMapMessage)(nil),                    // 8: proto.S2C_JumpMapMessage
	(*C2S_PlayerMoveMessage)(nil),                 // 9: proto.C2S_PlayerMoveMessage
	(*S2C_PlayerMoveMessage)(nil),                 // 10: proto.S2C_PlayerMoveMessage
	(*S2C_ScenePlayerEventMessage)(nil),           // 11: proto.S2C_ScenePlayerEventMessage
	(*C2S_PlayerEventChooseMessage)(nil),          // 12: proto.C2S_PlayerEventChooseMessage
	(*S2C_PlayerEventChooseMessage)(nil),          // 13: proto.S2C_PlayerEventChooseMessage
	(*S2C_GetPlayerEventChooseResultMessage)(nil), // 14: proto.S2C_GetPlayerEventChooseResultMessage
	(*S2C_BroadcastTeamJoinMessage)(nil),          // 15: proto.S2C_BroadcastTeamJoinMessage
	(*S2C_BroadcastTeamLeaveMessage)(nil),         // 16: proto.S2C_BroadcastTeamLeaveMessage
	(*C2S_ExitTeamMessage)(nil),                   // 17: proto.C2S_ExitTeamMessage
	(*S2C_ExitTeamMessage)(nil),                   // 18: proto.S2C_ExitTeamMessage
	(*C2S_RemoveMemberMessage)(nil),               // 19: proto.C2S_RemoveMemberMessage
	(*S2C_RemoveMemberMessage)(nil),               // 20: proto.S2C_RemoveMemberMessage
	(*C2S_ChangeLeaderMessage)(nil),               // 21: proto.C2S_ChangeLeaderMessage
	(*S2C_ChangeLeaderMessage)(nil),               // 22: proto.S2C_ChangeLeaderMessage
	(*S2C_BroadcastChangeLeaderMessage)(nil),      // 23: proto.S2C_BroadcastChangeLeaderMessage
	(*C2S_DisbandTeamMessage)(nil),                // 24: proto.C2S_DisbandTeamMessage
	(*S2C_DisbandTeamMessage)(nil),                // 25: proto.S2C_DisbandTeamMessage
	(*S2C_BroadcastDisbandTeamMessage)(nil),       // 26: proto.S2C_BroadcastDisbandTeamMessage
	(*pbCross.CrossSimplePlayer)(nil),             // 27: proto.CrossSimplePlayer
	(Response.Code)(0),                            // 28: proto.Response.Code
	(*Point)(nil),                                 // 29: proto.Point
	(*pbCross.PlayerEvent)(nil),                   // 30: proto.PlayerEvent
}
var file_pbGame_gameMap_proto_depIdxs = []int32{
	27, // 0: proto.S2C_EnterMapMessage.plr:type_name -> proto.CrossSimplePlayer
	28, // 1: proto.S2C_TeamJoinMessage.code:type_name -> proto.Response.Code
	28, // 2: proto.S2C_TeamInviteMessage.code:type_name -> proto.Response.Code
	28, // 3: proto.S2C_JumpMapMessage.code:type_name -> proto.Response.Code
	29, // 4: proto.C2S_PlayerMoveMessage.pos:type_name -> proto.Point
	28, // 5: proto.S2C_PlayerMoveMessage.code:type_name -> proto.Response.Code
	30, // 6: proto.S2C_ScenePlayerEventMessage.event:type_name -> proto.PlayerEvent
	28, // 7: proto.S2C_PlayerEventChooseMessage.code:type_name -> proto.Response.Code
	27, // 8: proto.S2C_GetPlayerEventChooseResultMessage.plr:type_name -> proto.CrossSimplePlayer
	28, // 9: proto.S2C_ExitTeamMessage.code:type_name -> proto.Response.Code
	28, // 10: proto.S2C_RemoveMemberMessage.code:type_name -> proto.Response.Code
	28, // 11: proto.S2C_ChangeLeaderMessage.code:type_name -> proto.Response.Code
	28, // 12: proto.S2C_DisbandTeamMessage.code:type_name -> proto.Response.Code
	13, // [13:13] is the sub-list for method output_type
	13, // [13:13] is the sub-list for method input_type
	13, // [13:13] is the sub-list for extension type_name
	13, // [13:13] is the sub-list for extension extendee
	0,  // [0:13] is the sub-list for field type_name
}

func init() { file_pbGame_gameMap_proto_init() }
func file_pbGame_gameMap_proto_init() {
	if File_pbGame_gameMap_proto != nil {
		return
	}
	file_pbGame_struct_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_pbGame_gameMap_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_EnterMapMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_gameMap_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_LeaveMapMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_gameMap_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_OtherMoveMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_gameMap_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_TeamJoinMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_gameMap_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_TeamJoinMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_gameMap_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_TeamInviteMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_gameMap_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_TeamInviteMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_gameMap_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_JumpMapMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_gameMap_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_JumpMapMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_gameMap_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_PlayerMoveMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_gameMap_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_PlayerMoveMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_gameMap_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_ScenePlayerEventMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_gameMap_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_PlayerEventChooseMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_gameMap_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_PlayerEventChooseMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_gameMap_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_GetPlayerEventChooseResultMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_gameMap_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_BroadcastTeamJoinMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_gameMap_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_BroadcastTeamLeaveMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_gameMap_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_ExitTeamMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_gameMap_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_ExitTeamMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_gameMap_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_RemoveMemberMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_gameMap_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_RemoveMemberMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_gameMap_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_ChangeLeaderMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_gameMap_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_ChangeLeaderMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_gameMap_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_BroadcastChangeLeaderMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_gameMap_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_DisbandTeamMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_gameMap_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_DisbandTeamMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_gameMap_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_BroadcastDisbandTeamMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pbGame_gameMap_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   27,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbGame_gameMap_proto_goTypes,
		DependencyIndexes: file_pbGame_gameMap_proto_depIdxs,
		MessageInfos:      file_pbGame_gameMap_proto_msgTypes,
	}.Build()
	File_pbGame_gameMap_proto = out.File
	file_pbGame_gameMap_proto_rawDesc = nil
	file_pbGame_gameMap_proto_goTypes = nil
	file_pbGame_gameMap_proto_depIdxs = nil
}
