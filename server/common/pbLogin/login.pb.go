// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v6.30.1
// source: pbLogin/login.proto

package pbLogin

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	LoginMethod "world/common/pbBase/LoginMethod"
	Response "world/common/pbBase/Response"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// After are messages.
type C2S_ClientCheckMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Version string `protobuf:"bytes,1,opt,name=version,proto3" json:"version,omitempty"` //客户端版本
}

func (x *C2S_ClientCheckMessage) Reset() {
	*x = C2S_ClientCheckMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbLogin_login_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_ClientCheckMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_ClientCheckMessage) ProtoMessage() {}

func (x *C2S_ClientCheckMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbLogin_login_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_ClientCheckMessage.ProtoReflect.Descriptor instead.
func (*C2S_ClientCheckMessage) Descriptor() ([]byte, []int) {
	return file_pbLogin_login_proto_rawDescGZIP(), []int{0}
}

func (x *C2S_ClientCheckMessage) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

type S2C_ClientCheckMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code       Response.Code `protobuf:"varint,1,opt,name=code,proto3,enum=proto.Response.Code" json:"code,omitempty"` //
	ServerTime int64         `protobuf:"varint,2,opt,name=serverTime,proto3" json:"serverTime,omitempty"`              //
}

func (x *S2C_ClientCheckMessage) Reset() {
	*x = S2C_ClientCheckMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbLogin_login_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_ClientCheckMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_ClientCheckMessage) ProtoMessage() {}

func (x *S2C_ClientCheckMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbLogin_login_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_ClientCheckMessage.ProtoReflect.Descriptor instead.
func (*S2C_ClientCheckMessage) Descriptor() ([]byte, []int) {
	return file_pbLogin_login_proto_rawDescGZIP(), []int{1}
}

func (x *S2C_ClientCheckMessage) GetCode() Response.Code {
	if x != nil {
		return x.Code
	}
	return Response.Code(0)
}

func (x *S2C_ClientCheckMessage) GetServerTime() int64 {
	if x != nil {
		return x.ServerTime
	}
	return 0
}

type C2S_ApplyLoginNoticeMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *C2S_ApplyLoginNoticeMessage) Reset() {
	*x = C2S_ApplyLoginNoticeMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbLogin_login_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_ApplyLoginNoticeMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_ApplyLoginNoticeMessage) ProtoMessage() {}

func (x *C2S_ApplyLoginNoticeMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbLogin_login_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_ApplyLoginNoticeMessage.ProtoReflect.Descriptor instead.
func (*C2S_ApplyLoginNoticeMessage) Descriptor() ([]byte, []int) {
	return file_pbLogin_login_proto_rawDescGZIP(), []int{2}
}

type S2C_ApplyLoginNoticeMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    Response.Code `protobuf:"varint,1,opt,name=code,proto3,enum=proto.Response.Code" json:"code,omitempty"` //
	Content string        `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`                     //
}

func (x *S2C_ApplyLoginNoticeMessage) Reset() {
	*x = S2C_ApplyLoginNoticeMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbLogin_login_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_ApplyLoginNoticeMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_ApplyLoginNoticeMessage) ProtoMessage() {}

func (x *S2C_ApplyLoginNoticeMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbLogin_login_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_ApplyLoginNoticeMessage.ProtoReflect.Descriptor instead.
func (*S2C_ApplyLoginNoticeMessage) Descriptor() ([]byte, []int) {
	return file_pbLogin_login_proto_rawDescGZIP(), []int{3}
}

func (x *S2C_ApplyLoginNoticeMessage) GetCode() Response.Code {
	if x != nil {
		return x.Code
	}
	return Response.Code(0)
}

func (x *S2C_ApplyLoginNoticeMessage) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

type C2S_LoginMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Method LoginMethod.Type  `protobuf:"varint,1,opt,name=method,proto3,enum=proto.LoginMethod.Type" json:"method,omitempty"`                                                        //登录方法
	Args   map[string]string `protobuf:"bytes,2,rep,name=args,proto3" json:"args,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` //登录参数
}

func (x *C2S_LoginMessage) Reset() {
	*x = C2S_LoginMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbLogin_login_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_LoginMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_LoginMessage) ProtoMessage() {}

func (x *C2S_LoginMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbLogin_login_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_LoginMessage.ProtoReflect.Descriptor instead.
func (*C2S_LoginMessage) Descriptor() ([]byte, []int) {
	return file_pbLogin_login_proto_rawDescGZIP(), []int{4}
}

func (x *C2S_LoginMessage) GetMethod() LoginMethod.Type {
	if x != nil {
		return x.Method
	}
	return LoginMethod.Type(0)
}

func (x *C2S_LoginMessage) GetArgs() map[string]string {
	if x != nil {
		return x.Args
	}
	return nil
}

type S2C_LoginMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code   Response.Code `protobuf:"varint,1,opt,name=code,proto3,enum=proto.Response.Code" json:"code,omitempty"` //
	Result *LoginResult  `protobuf:"bytes,2,opt,name=result,proto3" json:"result,omitempty"`                       //
}

func (x *S2C_LoginMessage) Reset() {
	*x = S2C_LoginMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbLogin_login_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_LoginMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_LoginMessage) ProtoMessage() {}

func (x *S2C_LoginMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbLogin_login_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_LoginMessage.ProtoReflect.Descriptor instead.
func (*S2C_LoginMessage) Descriptor() ([]byte, []int) {
	return file_pbLogin_login_proto_rawDescGZIP(), []int{5}
}

func (x *S2C_LoginMessage) GetCode() Response.Code {
	if x != nil {
		return x.Code
	}
	return Response.Code(0)
}

func (x *S2C_LoginMessage) GetResult() *LoginResult {
	if x != nil {
		return x.Result
	}
	return nil
}

type C2S_GetAreaLinesMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *C2S_GetAreaLinesMessage) Reset() {
	*x = C2S_GetAreaLinesMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbLogin_login_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_GetAreaLinesMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_GetAreaLinesMessage) ProtoMessage() {}

func (x *C2S_GetAreaLinesMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbLogin_login_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_GetAreaLinesMessage.ProtoReflect.Descriptor instead.
func (*C2S_GetAreaLinesMessage) Descriptor() ([]byte, []int) {
	return file_pbLogin_login_proto_rawDescGZIP(), []int{6}
}

type S2C_GetAreaLinesMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code   Response.Code `protobuf:"varint,1,opt,name=code,proto3,enum=proto.Response.Code" json:"code,omitempty"` //响应码
	LastId int32         `protobuf:"varint,2,opt,name=lastId,proto3" json:"lastId,omitempty"`                      //上一次选择的区服id
	List   []*AreaLine   `protobuf:"bytes,3,rep,name=list,proto3" json:"list,omitempty"`                           //区服数据
}

func (x *S2C_GetAreaLinesMessage) Reset() {
	*x = S2C_GetAreaLinesMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbLogin_login_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_GetAreaLinesMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_GetAreaLinesMessage) ProtoMessage() {}

func (x *S2C_GetAreaLinesMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbLogin_login_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_GetAreaLinesMessage.ProtoReflect.Descriptor instead.
func (*S2C_GetAreaLinesMessage) Descriptor() ([]byte, []int) {
	return file_pbLogin_login_proto_rawDescGZIP(), []int{7}
}

func (x *S2C_GetAreaLinesMessage) GetCode() Response.Code {
	if x != nil {
		return x.Code
	}
	return Response.Code(0)
}

func (x *S2C_GetAreaLinesMessage) GetLastId() int32 {
	if x != nil {
		return x.LastId
	}
	return 0
}

func (x *S2C_GetAreaLinesMessage) GetList() []*AreaLine {
	if x != nil {
		return x.List
	}
	return nil
}

type S2C_ErrorMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code Response.Code `protobuf:"varint,1,opt,name=code,proto3,enum=proto.Response.Code" json:"code,omitempty"` //码
}

func (x *S2C_ErrorMessage) Reset() {
	*x = S2C_ErrorMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbLogin_login_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_ErrorMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_ErrorMessage) ProtoMessage() {}

func (x *S2C_ErrorMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbLogin_login_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_ErrorMessage.ProtoReflect.Descriptor instead.
func (*S2C_ErrorMessage) Descriptor() ([]byte, []int) {
	return file_pbLogin_login_proto_rawDescGZIP(), []int{8}
}

func (x *S2C_ErrorMessage) GetCode() Response.Code {
	if x != nil {
		return x.Code
	}
	return Response.Code(0)
}

type C2S_SelectAreaMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"` //区服id
}

func (x *C2S_SelectAreaMessage) Reset() {
	*x = C2S_SelectAreaMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbLogin_login_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_SelectAreaMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_SelectAreaMessage) ProtoMessage() {}

func (x *C2S_SelectAreaMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbLogin_login_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_SelectAreaMessage.ProtoReflect.Descriptor instead.
func (*C2S_SelectAreaMessage) Descriptor() ([]byte, []int) {
	return file_pbLogin_login_proto_rawDescGZIP(), []int{9}
}

func (x *C2S_SelectAreaMessage) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

type S2C_SelectAreaMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code Response.Code `protobuf:"varint,1,opt,name=code,proto3,enum=proto.Response.Code" json:"code,omitempty"` //响应码
}

func (x *S2C_SelectAreaMessage) Reset() {
	*x = S2C_SelectAreaMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbLogin_login_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_SelectAreaMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_SelectAreaMessage) ProtoMessage() {}

func (x *S2C_SelectAreaMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbLogin_login_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_SelectAreaMessage.ProtoReflect.Descriptor instead.
func (*S2C_SelectAreaMessage) Descriptor() ([]byte, []int) {
	return file_pbLogin_login_proto_rawDescGZIP(), []int{10}
}

func (x *S2C_SelectAreaMessage) GetCode() Response.Code {
	if x != nil {
		return x.Code
	}
	return Response.Code(0)
}

type S2C_SyncServerTimeMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Time int64 `protobuf:"varint,1,opt,name=time,proto3" json:"time,omitempty"` //时间戳
}

func (x *S2C_SyncServerTimeMessage) Reset() {
	*x = S2C_SyncServerTimeMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbLogin_login_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_SyncServerTimeMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_SyncServerTimeMessage) ProtoMessage() {}

func (x *S2C_SyncServerTimeMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbLogin_login_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_SyncServerTimeMessage.ProtoReflect.Descriptor instead.
func (*S2C_SyncServerTimeMessage) Descriptor() ([]byte, []int) {
	return file_pbLogin_login_proto_rawDescGZIP(), []int{11}
}

func (x *S2C_SyncServerTimeMessage) GetTime() int64 {
	if x != nil {
		return x.Time
	}
	return 0
}

var File_pbLogin_login_proto protoreflect.FileDescriptor

var file_pbLogin_login_proto_rawDesc = []byte{
	0x0a, 0x13, 0x70, 0x62, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x2f, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x70, 0x62,
	0x42, 0x61, 0x73, 0x65, 0x2f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2f, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x24, 0x70, 0x62,
	0x42, 0x61, 0x73, 0x65, 0x2f, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64,
	0x2f, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x14, 0x70, 0x62, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x2f, 0x73, 0x74, 0x72, 0x75,
	0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x32, 0x0a, 0x16, 0x43, 0x32, 0x53, 0x5f,
	0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x62, 0x0a, 0x16,
	0x53, 0x32, 0x43, 0x5f, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65,
	0x22, 0x1d, 0x0a, 0x1b, 0x43, 0x32, 0x53, 0x5f, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x4c, 0x6f, 0x67,
	0x69, 0x6e, 0x4e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22,
	0x61, 0x0a, 0x1b, 0x53, 0x32, 0x43, 0x5f, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x4c, 0x6f, 0x67, 0x69,
	0x6e, 0x4e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x28,
	0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f,
	0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x22, 0xb3, 0x01, 0x0a, 0x10, 0x43, 0x32, 0x53, 0x5f, 0x4c, 0x6f, 0x67, 0x69, 0x6e,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x2f, 0x0a, 0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x2e, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x35, 0x0a, 0x04, 0x61, 0x72, 0x67, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43,
	0x32, 0x53, 0x5f, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e,
	0x41, 0x72, 0x67, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x04, 0x61, 0x72, 0x67, 0x73, 0x1a,
	0x37, 0x0a, 0x09, 0x41, 0x72, 0x67, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x68, 0x0a, 0x10, 0x53, 0x32, 0x43, 0x5f,
	0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x28, 0x0a, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x64, 0x65,
	0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x2a, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4c,
	0x6f, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x22, 0x19, 0x0a, 0x17, 0x43, 0x32, 0x53, 0x5f, 0x47, 0x65, 0x74, 0x41, 0x72, 0x65,
	0x61, 0x4c, 0x69, 0x6e, 0x65, 0x73, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x80, 0x01,
	0x0a, 0x17, 0x53, 0x32, 0x43, 0x5f, 0x47, 0x65, 0x74, 0x41, 0x72, 0x65, 0x61, 0x4c, 0x69, 0x6e,
	0x65, 0x73, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6c, 0x61, 0x73, 0x74, 0x49, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x06, 0x6c, 0x61, 0x73, 0x74, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x04, 0x6c,
	0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x41, 0x72, 0x65, 0x61, 0x4c, 0x69, 0x6e, 0x65, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74,
	0x22, 0x3c, 0x0a, 0x10, 0x53, 0x32, 0x43, 0x5f, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x27,
	0x0a, 0x15, 0x43, 0x32, 0x53, 0x5f, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x41, 0x72, 0x65, 0x61,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x22, 0x41, 0x0a, 0x15, 0x53, 0x32, 0x43, 0x5f, 0x53,
	0x65, 0x6c, 0x65, 0x63, 0x74, 0x41, 0x72, 0x65, 0x61, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e,
	0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x2f, 0x0a, 0x19, 0x53, 0x32,
	0x43, 0x5f, 0x53, 0x79, 0x6e, 0x63, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x42, 0x1e, 0x5a, 0x1c, 0x77,
	0x6f, 0x72, 0x6c, 0x64, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x62, 0x4c, 0x6f,
	0x67, 0x69, 0x6e, 0x3b, 0x70, 0x62, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_pbLogin_login_proto_rawDescOnce sync.Once
	file_pbLogin_login_proto_rawDescData = file_pbLogin_login_proto_rawDesc
)

func file_pbLogin_login_proto_rawDescGZIP() []byte {
	file_pbLogin_login_proto_rawDescOnce.Do(func() {
		file_pbLogin_login_proto_rawDescData = protoimpl.X.CompressGZIP(file_pbLogin_login_proto_rawDescData)
	})
	return file_pbLogin_login_proto_rawDescData
}

var file_pbLogin_login_proto_msgTypes = make([]protoimpl.MessageInfo, 13)
var file_pbLogin_login_proto_goTypes = []interface{}{
	(*C2S_ClientCheckMessage)(nil),      // 0: proto.C2S_ClientCheckMessage
	(*S2C_ClientCheckMessage)(nil),      // 1: proto.S2C_ClientCheckMessage
	(*C2S_ApplyLoginNoticeMessage)(nil), // 2: proto.C2S_ApplyLoginNoticeMessage
	(*S2C_ApplyLoginNoticeMessage)(nil), // 3: proto.S2C_ApplyLoginNoticeMessage
	(*C2S_LoginMessage)(nil),            // 4: proto.C2S_LoginMessage
	(*S2C_LoginMessage)(nil),            // 5: proto.S2C_LoginMessage
	(*C2S_GetAreaLinesMessage)(nil),     // 6: proto.C2S_GetAreaLinesMessage
	(*S2C_GetAreaLinesMessage)(nil),     // 7: proto.S2C_GetAreaLinesMessage
	(*S2C_ErrorMessage)(nil),            // 8: proto.S2C_ErrorMessage
	(*C2S_SelectAreaMessage)(nil),       // 9: proto.C2S_SelectAreaMessage
	(*S2C_SelectAreaMessage)(nil),       // 10: proto.S2C_SelectAreaMessage
	(*S2C_SyncServerTimeMessage)(nil),   // 11: proto.S2C_SyncServerTimeMessage
	nil,                                 // 12: proto.C2S_LoginMessage.ArgsEntry
	(Response.Code)(0),                  // 13: proto.Response.Code
	(LoginMethod.Type)(0),               // 14: proto.LoginMethod.Type
	(*LoginResult)(nil),                 // 15: proto.LoginResult
	(*AreaLine)(nil),                    // 16: proto.AreaLine
}
var file_pbLogin_login_proto_depIdxs = []int32{
	13, // 0: proto.S2C_ClientCheckMessage.code:type_name -> proto.Response.Code
	13, // 1: proto.S2C_ApplyLoginNoticeMessage.code:type_name -> proto.Response.Code
	14, // 2: proto.C2S_LoginMessage.method:type_name -> proto.LoginMethod.Type
	12, // 3: proto.C2S_LoginMessage.args:type_name -> proto.C2S_LoginMessage.ArgsEntry
	13, // 4: proto.S2C_LoginMessage.code:type_name -> proto.Response.Code
	15, // 5: proto.S2C_LoginMessage.result:type_name -> proto.LoginResult
	13, // 6: proto.S2C_GetAreaLinesMessage.code:type_name -> proto.Response.Code
	16, // 7: proto.S2C_GetAreaLinesMessage.list:type_name -> proto.AreaLine
	13, // 8: proto.S2C_ErrorMessage.code:type_name -> proto.Response.Code
	13, // 9: proto.S2C_SelectAreaMessage.code:type_name -> proto.Response.Code
	10, // [10:10] is the sub-list for method output_type
	10, // [10:10] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_pbLogin_login_proto_init() }
func file_pbLogin_login_proto_init() {
	if File_pbLogin_login_proto != nil {
		return
	}
	file_pbLogin_struct_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_pbLogin_login_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_ClientCheckMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbLogin_login_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_ClientCheckMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbLogin_login_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_ApplyLoginNoticeMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbLogin_login_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_ApplyLoginNoticeMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbLogin_login_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_LoginMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbLogin_login_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_LoginMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbLogin_login_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_GetAreaLinesMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbLogin_login_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_GetAreaLinesMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbLogin_login_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_ErrorMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbLogin_login_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_SelectAreaMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbLogin_login_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_SelectAreaMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbLogin_login_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_SyncServerTimeMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pbLogin_login_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   13,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbLogin_login_proto_goTypes,
		DependencyIndexes: file_pbLogin_login_proto_depIdxs,
		MessageInfos:      file_pbLogin_login_proto_msgTypes,
	}.Build()
	File_pbLogin_login_proto = out.File
	file_pbLogin_login_proto_rawDesc = nil
	file_pbLogin_login_proto_goTypes = nil
	file_pbLogin_login_proto_depIdxs = nil
}
