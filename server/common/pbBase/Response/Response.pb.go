// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v4.23.4
// source: pbBase/Response/Response.proto

package Response

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// After are enums.
// 错误提示
type Code int32

const (
	NoError                           Code = 0    //没有错误
	Unknown                           Code = -1   //未知错误
	AddItemBagIsFull                  Code = -4   //背包已满
	ErrClientVersionLow               Code = 1    //客户端版本过低
	ErrLoginMethod                    Code = 2    //错误的登录方式
	ErrUsernamePassword               Code = 3    //用户名或密码错误
	ErrLoginAtOther                   Code = 4    //账号在其他地方登录
	ErrPleaseLoginFirst               Code = 5    //请登录后再操作
	ErrNoAreaLine                     Code = 6    //没有区服开放数据
	ErrAreaLineId                     Code = 7    //区服id错误!
	ErrPlayerName                     Code = 8    //请输入正确的角色名!
	ErrPlayerNameLength               Code = 9    //角色名需要2-6个字符!
	ErrPlayerNameUnknownChar          Code = 10   //角色名包含未知字符!
	ErrDuplicatePlayerName            Code = 11   //角色名已经存在!
	ErrNotFoundForRoleId              Code = 12   //要登录的角色不存在
	ErrRoleNotBelongToCurrentUser     Code = 13   //角色不属于当前用户
	ErrPlayerStatus                   Code = 14   //角色当前状态不允许登录
	ErrMapClosed                      Code = 15   //地图暂时关闭，无法进入
	ConditionsAreNotMet               Code = 20   //条件不满足
	TaskNotExist                      Code = 21   //任务不存在。
	E22                               Code = 22   //回复太晚啦，信息已过期
	E23                               Code = 23   //当前与邀请人未在同个地图，通知已失效
	TheCurrentMapDoesNotHaveThisNpc   Code = 1001 //当前地图没有这个npc
	NPCDoesNotHaveThisTask            Code = 1002 //npc没有这个任务
	YouHaveAlreadyReceivedThisTask    Code = 1003 //您已经领取了这个任务
	CanNotAcceptTaskBecauseSoManyTask Code = 1004 //您身上未完成的任务太多了，暂时不能领取新任务
	SubmitFailedBecauseBagIsFull      Code = 1005 //提交任务失败，背包已满
	ErrBagResetSoFast                 Code = 2000 //背包整理过快
	ErrBagResetPanic                  Code = 2001 //背包整理异常
	ErrBagSellNoItem                  Code = 2002 //出售或丢弃物品时，没有任何选中物品，请整理背包后继续操作
	ErrBagSellErrItem                 Code = 2003 //出售或丢弃物品时，选择了错误的位置，请整理背包后继续操作
	ErrBagSellErrItemNum              Code = 2004 //出售或丢弃物品时，输入了错误的数量，请整理背包后继续操作
	ErrBagSellErrItemState            Code = 2005 //出售或丢弃物品时，选择的物品状态不支持出售或丢弃
	ErrBagIsFull                      Code = 2006 //背包已满
	ErrActorAttribute                 Code = 3000 //加点信息错误
	ErrActorAttributeDeduct           Code = 3001 //货币不足以支付洗点费用
	ErrActorAttributeType             Code = 3002 //属性不正确
	ErrActorAttributeMinus            Code = 3003 //属性点不能是负数
	ErrActorAttributeUse              Code = 3004 //可分配属性点不足
	LearnSkillNotFound                Code = 3005 //找不要学习的技能
	LearnSkillCanNotMoreThanMaxLevel  Code = 3006 //学习的技能不能超过超大等级
	LearnSkillLevelDoesNotMatch       Code = 3007 //学习的技能等级不符合当前等级
	LearnSkillSlotNotEnough           Code = 3008 //技能槽不足
	MoneyNotEnough                    Code = 3009 //金钱不足
	LearnSkillSkillPointNotEnough     Code = 3010 //技能点不足
	ItemSlotPosError                  Code = 3500 //物品位置不合法
	ItemCanNotEquip                   Code = 3501 //该物品不能被装备
	ErrEquipRequireNotMetOfStr        Code = 3502 //力量不够
	ErrEquipRequireNotMetOfCon        Code = 3503 //体质不够
	ErrEquipRequireNotMetOfAgi        Code = 3504 //敏捷不够
	ErrEquipRequireNotMetOfIlt        Code = 3505 //智力不够
	ErrEquipRequireNotMetOfWis        Code = 3506 //感知不够
	ErrEquipRequireNotMetOfLevel      Code = 3507 //等级不足
	ErrItemIsTimeout                  Code = 3508 //物品已过期
	ErrItemCanNotFindWearPos          Code = 3509 //找不到可以穿戴的位置
	EquipWearUnknownError             Code = 3510 //穿戴装备时发生未知错误
	ItemCanNotOperate                 Code = 3511 //该物品暂时不能被操作
	E3512                             Code = 3512 //物品不存在
	E3513                             Code = 3513 //不能在世界中使用!
	E3514                             Code = 3514 //使用物品的操作类型不正确!
	E3515                             Code = 3515 //宠物不存在!
	E3516                             Code = 3516 //穿戴中的装备无法操作!
	E3517                             Code = 3517 //40级以下宠物无法使用!
	E3518                             Code = 3518 //当前宠物寿命值已达最大值
	E3519                             Code = 3519 //宠物没有寿命，无法出战
	E3520                             Code = 3520 //宠物不存在
	E3521                             Code = 3521 //宠物潜能数据不存在
	E3522                             Code = 3522 //出战宠物不能放生！
	E3523                             Code = 3523 //已装备物品不能丢弃,请先卸下装备！
	E3524                             Code = 3524 //封印失败，宠物不存在！
	E3525                             Code = 3525 //宠物封印类型错误！
	E3526                             Code = 3526 //该宠物不能封印！
	E3527                             Code = 3527 //技能已满级，无法再学习!
	E3528                             Code = 3528 //当前技能书可学习等级达到上限，无法再学习!
	E3529                             Code = 3529 //技能配置不存在
	E3530                             Code = 3530 //宠物正在使用中，无法封印
	E3531                             Code = 3531 //不可学习天生可领悟技能
	E3532                             Code = 3532 //已达到可学习封印技能数量上限
	E3533                             Code = 3533 //技能不存在
	E3534                             Code = 3534 //技能释放时机不是主动释放
	E3535                             Code = 3535 //技能释放时机不是自动释放
	E3536                             Code = 3536 //自动释放技能已经设置满了，如需更换请取消无用设置
	E3537                             Code = 3537 //物品信息不符,请整理背包后重试.
	E3538                             Code = 3538 //摆摊中的物品不能操作!
	E3539                             Code = 3539 //这个物品不能进行该操作!
	E3540                             Code = 3540 //请将装备进阶后再来操作!
	E3541                             Code = 3541 //物品鉴定配置不存在!
	E3542                             Code = 3542 //缺少相应的鉴定卷轴!
	E3543                             Code = 3543 //缺少相应的高级鉴定卷轴!
	E3544                             Code = 3544 //缺少相应的进阶鉴定卷轴!
	E3545                             Code = 3545 //扣除道具失败!
	E3546                             Code = 3546 //装备强化卷轴不足!
	E3547                             Code = 3547 //强化星级已满!
	E3548                             Code = 3548 //绑定后才能升星!
	E3549                             Code = 3549 //这不是一个宝石!
	E3550                             Code = 3550 //这个物品不能镶嵌宝石!
	E3551                             Code = 3551 //镶嵌宝石数量已满!
	E3552                             Code = 3552 //这个装备已经镶嵌了其他类型的宝石!
	E3553                             Code = 3553 //宝石属性没有超过20,不能进行替换操作!
	E3554                             Code = 3554 //你所选择的宝石数量不足!
	E3555                             Code = 3555 //同类型宝石替换时请使用属性更高的宝石!
	E3556                             Code = 3556 //这个宝石属性不能替换!
	ErrBattleIdNotExist               Code = 4000 //找不到战斗对应的配置
	PetCfgNotExits                    Code = 5000 //宠物配置不存在
	PetTakeNumLimit                   Code = 5001 //宠物携带数量超限
	E6000                             Code = 6000 //队伍已满!
	E6001                             Code = 6001 //对方已经离线!
	E6002                             Code = 6002 //对方已有队伍!
	E6003                             Code = 6003 //你已经加入了队伍!
	E6004                             Code = 6004 //你与对方不在同一个地图!
	E6005                             Code = 6005 //队伍人数已满!
	E6006                             Code = 6006 //
)

// Enum value maps for Code.
var (
	Code_name = map[int32]string{
		0:    "NoError",
		-1:   "Unknown",
		-4:   "AddItemBagIsFull",
		1:    "ErrClientVersionLow",
		2:    "ErrLoginMethod",
		3:    "ErrUsernamePassword",
		4:    "ErrLoginAtOther",
		5:    "ErrPleaseLoginFirst",
		6:    "ErrNoAreaLine",
		7:    "ErrAreaLineId",
		8:    "ErrPlayerName",
		9:    "ErrPlayerNameLength",
		10:   "ErrPlayerNameUnknownChar",
		11:   "ErrDuplicatePlayerName",
		12:   "ErrNotFoundForRoleId",
		13:   "ErrRoleNotBelongToCurrentUser",
		14:   "ErrPlayerStatus",
		15:   "ErrMapClosed",
		20:   "ConditionsAreNotMet",
		21:   "TaskNotExist",
		22:   "E22",
		23:   "E23",
		1001: "TheCurrentMapDoesNotHaveThisNpc",
		1002: "NPCDoesNotHaveThisTask",
		1003: "YouHaveAlreadyReceivedThisTask",
		1004: "CanNotAcceptTaskBecauseSoManyTask",
		1005: "SubmitFailedBecauseBagIsFull",
		2000: "ErrBagResetSoFast",
		2001: "ErrBagResetPanic",
		2002: "ErrBagSellNoItem",
		2003: "ErrBagSellErrItem",
		2004: "ErrBagSellErrItemNum",
		2005: "ErrBagSellErrItemState",
		2006: "ErrBagIsFull",
		3000: "ErrActorAttribute",
		3001: "ErrActorAttributeDeduct",
		3002: "ErrActorAttributeType",
		3003: "ErrActorAttributeMinus",
		3004: "ErrActorAttributeUse",
		3005: "LearnSkillNotFound",
		3006: "LearnSkillCanNotMoreThanMaxLevel",
		3007: "LearnSkillLevelDoesNotMatch",
		3008: "LearnSkillSlotNotEnough",
		3009: "MoneyNotEnough",
		3010: "LearnSkillSkillPointNotEnough",
		3500: "ItemSlotPosError",
		3501: "ItemCanNotEquip",
		3502: "ErrEquipRequireNotMetOfStr",
		3503: "ErrEquipRequireNotMetOfCon",
		3504: "ErrEquipRequireNotMetOfAgi",
		3505: "ErrEquipRequireNotMetOfIlt",
		3506: "ErrEquipRequireNotMetOfWis",
		3507: "ErrEquipRequireNotMetOfLevel",
		3508: "ErrItemIsTimeout",
		3509: "ErrItemCanNotFindWearPos",
		3510: "EquipWearUnknownError",
		3511: "ItemCanNotOperate",
		3512: "E3512",
		3513: "E3513",
		3514: "E3514",
		3515: "E3515",
		3516: "E3516",
		3517: "E3517",
		3518: "E3518",
		3519: "E3519",
		3520: "E3520",
		3521: "E3521",
		3522: "E3522",
		3523: "E3523",
		3524: "E3524",
		3525: "E3525",
		3526: "E3526",
		3527: "E3527",
		3528: "E3528",
		3529: "E3529",
		3530: "E3530",
		3531: "E3531",
		3532: "E3532",
		3533: "E3533",
		3534: "E3534",
		3535: "E3535",
		3536: "E3536",
		3537: "E3537",
		3538: "E3538",
		3539: "E3539",
		3540: "E3540",
		3541: "E3541",
		3542: "E3542",
		3543: "E3543",
		3544: "E3544",
		3545: "E3545",
		3546: "E3546",
		3547: "E3547",
		3548: "E3548",
		3549: "E3549",
		3550: "E3550",
		3551: "E3551",
		3552: "E3552",
		3553: "E3553",
		3554: "E3554",
		3555: "E3555",
		3556: "E3556",
		4000: "ErrBattleIdNotExist",
		5000: "PetCfgNotExits",
		5001: "PetTakeNumLimit",
		6000: "E6000",
		6001: "E6001",
		6002: "E6002",
		6003: "E6003",
		6004: "E6004",
		6005: "E6005",
		6006: "E6006",
	}
	Code_value = map[string]int32{
		"NoError":                           0,
		"Unknown":                           -1,
		"AddItemBagIsFull":                  -4,
		"ErrClientVersionLow":               1,
		"ErrLoginMethod":                    2,
		"ErrUsernamePassword":               3,
		"ErrLoginAtOther":                   4,
		"ErrPleaseLoginFirst":               5,
		"ErrNoAreaLine":                     6,
		"ErrAreaLineId":                     7,
		"ErrPlayerName":                     8,
		"ErrPlayerNameLength":               9,
		"ErrPlayerNameUnknownChar":          10,
		"ErrDuplicatePlayerName":            11,
		"ErrNotFoundForRoleId":              12,
		"ErrRoleNotBelongToCurrentUser":     13,
		"ErrPlayerStatus":                   14,
		"ErrMapClosed":                      15,
		"ConditionsAreNotMet":               20,
		"TaskNotExist":                      21,
		"E22":                               22,
		"E23":                               23,
		"TheCurrentMapDoesNotHaveThisNpc":   1001,
		"NPCDoesNotHaveThisTask":            1002,
		"YouHaveAlreadyReceivedThisTask":    1003,
		"CanNotAcceptTaskBecauseSoManyTask": 1004,
		"SubmitFailedBecauseBagIsFull":      1005,
		"ErrBagResetSoFast":                 2000,
		"ErrBagResetPanic":                  2001,
		"ErrBagSellNoItem":                  2002,
		"ErrBagSellErrItem":                 2003,
		"ErrBagSellErrItemNum":              2004,
		"ErrBagSellErrItemState":            2005,
		"ErrBagIsFull":                      2006,
		"ErrActorAttribute":                 3000,
		"ErrActorAttributeDeduct":           3001,
		"ErrActorAttributeType":             3002,
		"ErrActorAttributeMinus":            3003,
		"ErrActorAttributeUse":              3004,
		"LearnSkillNotFound":                3005,
		"LearnSkillCanNotMoreThanMaxLevel":  3006,
		"LearnSkillLevelDoesNotMatch":       3007,
		"LearnSkillSlotNotEnough":           3008,
		"MoneyNotEnough":                    3009,
		"LearnSkillSkillPointNotEnough":     3010,
		"ItemSlotPosError":                  3500,
		"ItemCanNotEquip":                   3501,
		"ErrEquipRequireNotMetOfStr":        3502,
		"ErrEquipRequireNotMetOfCon":        3503,
		"ErrEquipRequireNotMetOfAgi":        3504,
		"ErrEquipRequireNotMetOfIlt":        3505,
		"ErrEquipRequireNotMetOfWis":        3506,
		"ErrEquipRequireNotMetOfLevel":      3507,
		"ErrItemIsTimeout":                  3508,
		"ErrItemCanNotFindWearPos":          3509,
		"EquipWearUnknownError":             3510,
		"ItemCanNotOperate":                 3511,
		"E3512":                             3512,
		"E3513":                             3513,
		"E3514":                             3514,
		"E3515":                             3515,
		"E3516":                             3516,
		"E3517":                             3517,
		"E3518":                             3518,
		"E3519":                             3519,
		"E3520":                             3520,
		"E3521":                             3521,
		"E3522":                             3522,
		"E3523":                             3523,
		"E3524":                             3524,
		"E3525":                             3525,
		"E3526":                             3526,
		"E3527":                             3527,
		"E3528":                             3528,
		"E3529":                             3529,
		"E3530":                             3530,
		"E3531":                             3531,
		"E3532":                             3532,
		"E3533":                             3533,
		"E3534":                             3534,
		"E3535":                             3535,
		"E3536":                             3536,
		"E3537":                             3537,
		"E3538":                             3538,
		"E3539":                             3539,
		"E3540":                             3540,
		"E3541":                             3541,
		"E3542":                             3542,
		"E3543":                             3543,
		"E3544":                             3544,
		"E3545":                             3545,
		"E3546":                             3546,
		"E3547":                             3547,
		"E3548":                             3548,
		"E3549":                             3549,
		"E3550":                             3550,
		"E3551":                             3551,
		"E3552":                             3552,
		"E3553":                             3553,
		"E3554":                             3554,
		"E3555":                             3555,
		"E3556":                             3556,
		"ErrBattleIdNotExist":               4000,
		"PetCfgNotExits":                    5000,
		"PetTakeNumLimit":                   5001,
		"E6000":                             6000,
		"E6001":                             6001,
		"E6002":                             6002,
		"E6003":                             6003,
		"E6004":                             6004,
		"E6005":                             6005,
		"E6006":                             6006,
	}
)

func (x Code) Enum() *Code {
	p := new(Code)
	*p = x
	return p
}

func (x Code) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Code) Descriptor() protoreflect.EnumDescriptor {
	return file_pbBase_Response_Response_proto_enumTypes[0].Descriptor()
}

func (Code) Type() protoreflect.EnumType {
	return &file_pbBase_Response_Response_proto_enumTypes[0]
}

func (x Code) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Code.Descriptor instead.
func (Code) EnumDescriptor() ([]byte, []int) {
	return file_pbBase_Response_Response_proto_rawDescGZIP(), []int{0}
}

var File_pbBase_Response_Response_proto protoreflect.FileDescriptor

var file_pbBase_Response_Response_proto_rawDesc = []byte{
	0x0a, 0x1e, 0x70, 0x62, 0x42, 0x61, 0x73, 0x65, 0x2f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x2f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x0e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x2a, 0x9c, 0x11, 0x0a, 0x04, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x4e, 0x6f, 0x45,
	0x72, 0x72, 0x6f, 0x72, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x07, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77,
	0x6e, 0x10, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x01, 0x12, 0x1d, 0x0a, 0x10,
	0x41, 0x64, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x42, 0x61, 0x67, 0x49, 0x73, 0x46, 0x75, 0x6c, 0x6c,
	0x10, 0xfc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x01, 0x12, 0x17, 0x0a, 0x13, 0x45,
	0x72, 0x72, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x4c,
	0x6f, 0x77, 0x10, 0x01, 0x12, 0x12, 0x0a, 0x0e, 0x45, 0x72, 0x72, 0x4c, 0x6f, 0x67, 0x69, 0x6e,
	0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x10, 0x02, 0x12, 0x17, 0x0a, 0x13, 0x45, 0x72, 0x72, 0x55,
	0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x10,
	0x03, 0x12, 0x13, 0x0a, 0x0f, 0x45, 0x72, 0x72, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x41, 0x74, 0x4f,
	0x74, 0x68, 0x65, 0x72, 0x10, 0x04, 0x12, 0x17, 0x0a, 0x13, 0x45, 0x72, 0x72, 0x50, 0x6c, 0x65,
	0x61, 0x73, 0x65, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x46, 0x69, 0x72, 0x73, 0x74, 0x10, 0x05, 0x12,
	0x11, 0x0a, 0x0d, 0x45, 0x72, 0x72, 0x4e, 0x6f, 0x41, 0x72, 0x65, 0x61, 0x4c, 0x69, 0x6e, 0x65,
	0x10, 0x06, 0x12, 0x11, 0x0a, 0x0d, 0x45, 0x72, 0x72, 0x41, 0x72, 0x65, 0x61, 0x4c, 0x69, 0x6e,
	0x65, 0x49, 0x64, 0x10, 0x07, 0x12, 0x11, 0x0a, 0x0d, 0x45, 0x72, 0x72, 0x50, 0x6c, 0x61, 0x79,
	0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x10, 0x08, 0x12, 0x17, 0x0a, 0x13, 0x45, 0x72, 0x72, 0x50,
	0x6c, 0x61, 0x79, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x4c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x10,
	0x09, 0x12, 0x1c, 0x0a, 0x18, 0x45, 0x72, 0x72, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x4e, 0x61,
	0x6d, 0x65, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x43, 0x68, 0x61, 0x72, 0x10, 0x0a, 0x12,
	0x1a, 0x0a, 0x16, 0x45, 0x72, 0x72, 0x44, 0x75, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x65, 0x50,
	0x6c, 0x61, 0x79, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x10, 0x0b, 0x12, 0x18, 0x0a, 0x14, 0x45,
	0x72, 0x72, 0x4e, 0x6f, 0x74, 0x46, 0x6f, 0x75, 0x6e, 0x64, 0x46, 0x6f, 0x72, 0x52, 0x6f, 0x6c,
	0x65, 0x49, 0x64, 0x10, 0x0c, 0x12, 0x21, 0x0a, 0x1d, 0x45, 0x72, 0x72, 0x52, 0x6f, 0x6c, 0x65,
	0x4e, 0x6f, 0x74, 0x42, 0x65, 0x6c, 0x6f, 0x6e, 0x67, 0x54, 0x6f, 0x43, 0x75, 0x72, 0x72, 0x65,
	0x6e, 0x74, 0x55, 0x73, 0x65, 0x72, 0x10, 0x0d, 0x12, 0x13, 0x0a, 0x0f, 0x45, 0x72, 0x72, 0x50,
	0x6c, 0x61, 0x79, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x10, 0x0e, 0x12, 0x10, 0x0a,
	0x0c, 0x45, 0x72, 0x72, 0x4d, 0x61, 0x70, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x10, 0x0f, 0x12,
	0x17, 0x0a, 0x13, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x41, 0x72, 0x65,
	0x4e, 0x6f, 0x74, 0x4d, 0x65, 0x74, 0x10, 0x14, 0x12, 0x10, 0x0a, 0x0c, 0x54, 0x61, 0x73, 0x6b,
	0x4e, 0x6f, 0x74, 0x45, 0x78, 0x69, 0x73, 0x74, 0x10, 0x15, 0x12, 0x07, 0x0a, 0x03, 0x45, 0x32,
	0x32, 0x10, 0x16, 0x12, 0x07, 0x0a, 0x03, 0x45, 0x32, 0x33, 0x10, 0x17, 0x12, 0x24, 0x0a, 0x1f,
	0x54, 0x68, 0x65, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x4d, 0x61, 0x70, 0x44, 0x6f, 0x65,
	0x73, 0x4e, 0x6f, 0x74, 0x48, 0x61, 0x76, 0x65, 0x54, 0x68, 0x69, 0x73, 0x4e, 0x70, 0x63, 0x10,
	0xe9, 0x07, 0x12, 0x1b, 0x0a, 0x16, 0x4e, 0x50, 0x43, 0x44, 0x6f, 0x65, 0x73, 0x4e, 0x6f, 0x74,
	0x48, 0x61, 0x76, 0x65, 0x54, 0x68, 0x69, 0x73, 0x54, 0x61, 0x73, 0x6b, 0x10, 0xea, 0x07, 0x12,
	0x23, 0x0a, 0x1e, 0x59, 0x6f, 0x75, 0x48, 0x61, 0x76, 0x65, 0x41, 0x6c, 0x72, 0x65, 0x61, 0x64,
	0x79, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x64, 0x54, 0x68, 0x69, 0x73, 0x54, 0x61, 0x73,
	0x6b, 0x10, 0xeb, 0x07, 0x12, 0x26, 0x0a, 0x21, 0x43, 0x61, 0x6e, 0x4e, 0x6f, 0x74, 0x41, 0x63,
	0x63, 0x65, 0x70, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x42, 0x65, 0x63, 0x61, 0x75, 0x73, 0x65, 0x53,
	0x6f, 0x4d, 0x61, 0x6e, 0x79, 0x54, 0x61, 0x73, 0x6b, 0x10, 0xec, 0x07, 0x12, 0x21, 0x0a, 0x1c,
	0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x46, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x42, 0x65, 0x63, 0x61,
	0x75, 0x73, 0x65, 0x42, 0x61, 0x67, 0x49, 0x73, 0x46, 0x75, 0x6c, 0x6c, 0x10, 0xed, 0x07, 0x12,
	0x16, 0x0a, 0x11, 0x45, 0x72, 0x72, 0x42, 0x61, 0x67, 0x52, 0x65, 0x73, 0x65, 0x74, 0x53, 0x6f,
	0x46, 0x61, 0x73, 0x74, 0x10, 0xd0, 0x0f, 0x12, 0x15, 0x0a, 0x10, 0x45, 0x72, 0x72, 0x42, 0x61,
	0x67, 0x52, 0x65, 0x73, 0x65, 0x74, 0x50, 0x61, 0x6e, 0x69, 0x63, 0x10, 0xd1, 0x0f, 0x12, 0x15,
	0x0a, 0x10, 0x45, 0x72, 0x72, 0x42, 0x61, 0x67, 0x53, 0x65, 0x6c, 0x6c, 0x4e, 0x6f, 0x49, 0x74,
	0x65, 0x6d, 0x10, 0xd2, 0x0f, 0x12, 0x16, 0x0a, 0x11, 0x45, 0x72, 0x72, 0x42, 0x61, 0x67, 0x53,
	0x65, 0x6c, 0x6c, 0x45, 0x72, 0x72, 0x49, 0x74, 0x65, 0x6d, 0x10, 0xd3, 0x0f, 0x12, 0x19, 0x0a,
	0x14, 0x45, 0x72, 0x72, 0x42, 0x61, 0x67, 0x53, 0x65, 0x6c, 0x6c, 0x45, 0x72, 0x72, 0x49, 0x74,
	0x65, 0x6d, 0x4e, 0x75, 0x6d, 0x10, 0xd4, 0x0f, 0x12, 0x1b, 0x0a, 0x16, 0x45, 0x72, 0x72, 0x42,
	0x61, 0x67, 0x53, 0x65, 0x6c, 0x6c, 0x45, 0x72, 0x72, 0x49, 0x74, 0x65, 0x6d, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x10, 0xd5, 0x0f, 0x12, 0x11, 0x0a, 0x0c, 0x45, 0x72, 0x72, 0x42, 0x61, 0x67, 0x49,
	0x73, 0x46, 0x75, 0x6c, 0x6c, 0x10, 0xd6, 0x0f, 0x12, 0x16, 0x0a, 0x11, 0x45, 0x72, 0x72, 0x41,
	0x63, 0x74, 0x6f, 0x72, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x10, 0xb8, 0x17,
	0x12, 0x1c, 0x0a, 0x17, 0x45, 0x72, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x41, 0x74, 0x74, 0x72,
	0x69, 0x62, 0x75, 0x74, 0x65, 0x44, 0x65, 0x64, 0x75, 0x63, 0x74, 0x10, 0xb9, 0x17, 0x12, 0x1a,
	0x0a, 0x15, 0x45, 0x72, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62,
	0x75, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x10, 0xba, 0x17, 0x12, 0x1b, 0x0a, 0x16, 0x45, 0x72,
	0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x4d,
	0x69, 0x6e, 0x75, 0x73, 0x10, 0xbb, 0x17, 0x12, 0x19, 0x0a, 0x14, 0x45, 0x72, 0x72, 0x41, 0x63,
	0x74, 0x6f, 0x72, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x55, 0x73, 0x65, 0x10,
	0xbc, 0x17, 0x12, 0x17, 0x0a, 0x12, 0x4c, 0x65, 0x61, 0x72, 0x6e, 0x53, 0x6b, 0x69, 0x6c, 0x6c,
	0x4e, 0x6f, 0x74, 0x46, 0x6f, 0x75, 0x6e, 0x64, 0x10, 0xbd, 0x17, 0x12, 0x25, 0x0a, 0x20, 0x4c,
	0x65, 0x61, 0x72, 0x6e, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x43, 0x61, 0x6e, 0x4e, 0x6f, 0x74, 0x4d,
	0x6f, 0x72, 0x65, 0x54, 0x68, 0x61, 0x6e, 0x4d, 0x61, 0x78, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x10,
	0xbe, 0x17, 0x12, 0x20, 0x0a, 0x1b, 0x4c, 0x65, 0x61, 0x72, 0x6e, 0x53, 0x6b, 0x69, 0x6c, 0x6c,
	0x4c, 0x65, 0x76, 0x65, 0x6c, 0x44, 0x6f, 0x65, 0x73, 0x4e, 0x6f, 0x74, 0x4d, 0x61, 0x74, 0x63,
	0x68, 0x10, 0xbf, 0x17, 0x12, 0x1c, 0x0a, 0x17, 0x4c, 0x65, 0x61, 0x72, 0x6e, 0x53, 0x6b, 0x69,
	0x6c, 0x6c, 0x53, 0x6c, 0x6f, 0x74, 0x4e, 0x6f, 0x74, 0x45, 0x6e, 0x6f, 0x75, 0x67, 0x68, 0x10,
	0xc0, 0x17, 0x12, 0x13, 0x0a, 0x0e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x4e, 0x6f, 0x74, 0x45, 0x6e,
	0x6f, 0x75, 0x67, 0x68, 0x10, 0xc1, 0x17, 0x12, 0x22, 0x0a, 0x1d, 0x4c, 0x65, 0x61, 0x72, 0x6e,
	0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x4e,
	0x6f, 0x74, 0x45, 0x6e, 0x6f, 0x75, 0x67, 0x68, 0x10, 0xc2, 0x17, 0x12, 0x15, 0x0a, 0x10, 0x49,
	0x74, 0x65, 0x6d, 0x53, 0x6c, 0x6f, 0x74, 0x50, 0x6f, 0x73, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x10,
	0xac, 0x1b, 0x12, 0x14, 0x0a, 0x0f, 0x49, 0x74, 0x65, 0x6d, 0x43, 0x61, 0x6e, 0x4e, 0x6f, 0x74,
	0x45, 0x71, 0x75, 0x69, 0x70, 0x10, 0xad, 0x1b, 0x12, 0x1f, 0x0a, 0x1a, 0x45, 0x72, 0x72, 0x45,
	0x71, 0x75, 0x69, 0x70, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x4e, 0x6f, 0x74, 0x4d, 0x65,
	0x74, 0x4f, 0x66, 0x53, 0x74, 0x72, 0x10, 0xae, 0x1b, 0x12, 0x1f, 0x0a, 0x1a, 0x45, 0x72, 0x72,
	0x45, 0x71, 0x75, 0x69, 0x70, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x4e, 0x6f, 0x74, 0x4d,
	0x65, 0x74, 0x4f, 0x66, 0x43, 0x6f, 0x6e, 0x10, 0xaf, 0x1b, 0x12, 0x1f, 0x0a, 0x1a, 0x45, 0x72,
	0x72, 0x45, 0x71, 0x75, 0x69, 0x70, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x4e, 0x6f, 0x74,
	0x4d, 0x65, 0x74, 0x4f, 0x66, 0x41, 0x67, 0x69, 0x10, 0xb0, 0x1b, 0x12, 0x1f, 0x0a, 0x1a, 0x45,
	0x72, 0x72, 0x45, 0x71, 0x75, 0x69, 0x70, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x4e, 0x6f,
	0x74, 0x4d, 0x65, 0x74, 0x4f, 0x66, 0x49, 0x6c, 0x74, 0x10, 0xb1, 0x1b, 0x12, 0x1f, 0x0a, 0x1a,
	0x45, 0x72, 0x72, 0x45, 0x71, 0x75, 0x69, 0x70, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x4e,
	0x6f, 0x74, 0x4d, 0x65, 0x74, 0x4f, 0x66, 0x57, 0x69, 0x73, 0x10, 0xb2, 0x1b, 0x12, 0x21, 0x0a,
	0x1c, 0x45, 0x72, 0x72, 0x45, 0x71, 0x75, 0x69, 0x70, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x4e, 0x6f, 0x74, 0x4d, 0x65, 0x74, 0x4f, 0x66, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x10, 0xb3, 0x1b,
	0x12, 0x15, 0x0a, 0x10, 0x45, 0x72, 0x72, 0x49, 0x74, 0x65, 0x6d, 0x49, 0x73, 0x54, 0x69, 0x6d,
	0x65, 0x6f, 0x75, 0x74, 0x10, 0xb4, 0x1b, 0x12, 0x1d, 0x0a, 0x18, 0x45, 0x72, 0x72, 0x49, 0x74,
	0x65, 0x6d, 0x43, 0x61, 0x6e, 0x4e, 0x6f, 0x74, 0x46, 0x69, 0x6e, 0x64, 0x57, 0x65, 0x61, 0x72,
	0x50, 0x6f, 0x73, 0x10, 0xb5, 0x1b, 0x12, 0x1a, 0x0a, 0x15, 0x45, 0x71, 0x75, 0x69, 0x70, 0x57,
	0x65, 0x61, 0x72, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x10,
	0xb6, 0x1b, 0x12, 0x16, 0x0a, 0x11, 0x49, 0x74, 0x65, 0x6d, 0x43, 0x61, 0x6e, 0x4e, 0x6f, 0x74,
	0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x10, 0xb7, 0x1b, 0x12, 0x0a, 0x0a, 0x05, 0x45, 0x33,
	0x35, 0x31, 0x32, 0x10, 0xb8, 0x1b, 0x12, 0x0a, 0x0a, 0x05, 0x45, 0x33, 0x35, 0x31, 0x33, 0x10,
	0xb9, 0x1b, 0x12, 0x0a, 0x0a, 0x05, 0x45, 0x33, 0x35, 0x31, 0x34, 0x10, 0xba, 0x1b, 0x12, 0x0a,
	0x0a, 0x05, 0x45, 0x33, 0x35, 0x31, 0x35, 0x10, 0xbb, 0x1b, 0x12, 0x0a, 0x0a, 0x05, 0x45, 0x33,
	0x35, 0x31, 0x36, 0x10, 0xbc, 0x1b, 0x12, 0x0a, 0x0a, 0x05, 0x45, 0x33, 0x35, 0x31, 0x37, 0x10,
	0xbd, 0x1b, 0x12, 0x0a, 0x0a, 0x05, 0x45, 0x33, 0x35, 0x31, 0x38, 0x10, 0xbe, 0x1b, 0x12, 0x0a,
	0x0a, 0x05, 0x45, 0x33, 0x35, 0x31, 0x39, 0x10, 0xbf, 0x1b, 0x12, 0x0a, 0x0a, 0x05, 0x45, 0x33,
	0x35, 0x32, 0x30, 0x10, 0xc0, 0x1b, 0x12, 0x0a, 0x0a, 0x05, 0x45, 0x33, 0x35, 0x32, 0x31, 0x10,
	0xc1, 0x1b, 0x12, 0x0a, 0x0a, 0x05, 0x45, 0x33, 0x35, 0x32, 0x32, 0x10, 0xc2, 0x1b, 0x12, 0x0a,
	0x0a, 0x05, 0x45, 0x33, 0x35, 0x32, 0x33, 0x10, 0xc3, 0x1b, 0x12, 0x0a, 0x0a, 0x05, 0x45, 0x33,
	0x35, 0x32, 0x34, 0x10, 0xc4, 0x1b, 0x12, 0x0a, 0x0a, 0x05, 0x45, 0x33, 0x35, 0x32, 0x35, 0x10,
	0xc5, 0x1b, 0x12, 0x0a, 0x0a, 0x05, 0x45, 0x33, 0x35, 0x32, 0x36, 0x10, 0xc6, 0x1b, 0x12, 0x0a,
	0x0a, 0x05, 0x45, 0x33, 0x35, 0x32, 0x37, 0x10, 0xc7, 0x1b, 0x12, 0x0a, 0x0a, 0x05, 0x45, 0x33,
	0x35, 0x32, 0x38, 0x10, 0xc8, 0x1b, 0x12, 0x0a, 0x0a, 0x05, 0x45, 0x33, 0x35, 0x32, 0x39, 0x10,
	0xc9, 0x1b, 0x12, 0x0a, 0x0a, 0x05, 0x45, 0x33, 0x35, 0x33, 0x30, 0x10, 0xca, 0x1b, 0x12, 0x0a,
	0x0a, 0x05, 0x45, 0x33, 0x35, 0x33, 0x31, 0x10, 0xcb, 0x1b, 0x12, 0x0a, 0x0a, 0x05, 0x45, 0x33,
	0x35, 0x33, 0x32, 0x10, 0xcc, 0x1b, 0x12, 0x0a, 0x0a, 0x05, 0x45, 0x33, 0x35, 0x33, 0x33, 0x10,
	0xcd, 0x1b, 0x12, 0x0a, 0x0a, 0x05, 0x45, 0x33, 0x35, 0x33, 0x34, 0x10, 0xce, 0x1b, 0x12, 0x0a,
	0x0a, 0x05, 0x45, 0x33, 0x35, 0x33, 0x35, 0x10, 0xcf, 0x1b, 0x12, 0x0a, 0x0a, 0x05, 0x45, 0x33,
	0x35, 0x33, 0x36, 0x10, 0xd0, 0x1b, 0x12, 0x0a, 0x0a, 0x05, 0x45, 0x33, 0x35, 0x33, 0x37, 0x10,
	0xd1, 0x1b, 0x12, 0x0a, 0x0a, 0x05, 0x45, 0x33, 0x35, 0x33, 0x38, 0x10, 0xd2, 0x1b, 0x12, 0x0a,
	0x0a, 0x05, 0x45, 0x33, 0x35, 0x33, 0x39, 0x10, 0xd3, 0x1b, 0x12, 0x0a, 0x0a, 0x05, 0x45, 0x33,
	0x35, 0x34, 0x30, 0x10, 0xd4, 0x1b, 0x12, 0x0a, 0x0a, 0x05, 0x45, 0x33, 0x35, 0x34, 0x31, 0x10,
	0xd5, 0x1b, 0x12, 0x0a, 0x0a, 0x05, 0x45, 0x33, 0x35, 0x34, 0x32, 0x10, 0xd6, 0x1b, 0x12, 0x0a,
	0x0a, 0x05, 0x45, 0x33, 0x35, 0x34, 0x33, 0x10, 0xd7, 0x1b, 0x12, 0x0a, 0x0a, 0x05, 0x45, 0x33,
	0x35, 0x34, 0x34, 0x10, 0xd8, 0x1b, 0x12, 0x0a, 0x0a, 0x05, 0x45, 0x33, 0x35, 0x34, 0x35, 0x10,
	0xd9, 0x1b, 0x12, 0x0a, 0x0a, 0x05, 0x45, 0x33, 0x35, 0x34, 0x36, 0x10, 0xda, 0x1b, 0x12, 0x0a,
	0x0a, 0x05, 0x45, 0x33, 0x35, 0x34, 0x37, 0x10, 0xdb, 0x1b, 0x12, 0x0a, 0x0a, 0x05, 0x45, 0x33,
	0x35, 0x34, 0x38, 0x10, 0xdc, 0x1b, 0x12, 0x0a, 0x0a, 0x05, 0x45, 0x33, 0x35, 0x34, 0x39, 0x10,
	0xdd, 0x1b, 0x12, 0x0a, 0x0a, 0x05, 0x45, 0x33, 0x35, 0x35, 0x30, 0x10, 0xde, 0x1b, 0x12, 0x0a,
	0x0a, 0x05, 0x45, 0x33, 0x35, 0x35, 0x31, 0x10, 0xdf, 0x1b, 0x12, 0x0a, 0x0a, 0x05, 0x45, 0x33,
	0x35, 0x35, 0x32, 0x10, 0xe0, 0x1b, 0x12, 0x0a, 0x0a, 0x05, 0x45, 0x33, 0x35, 0x35, 0x33, 0x10,
	0xe1, 0x1b, 0x12, 0x0a, 0x0a, 0x05, 0x45, 0x33, 0x35, 0x35, 0x34, 0x10, 0xe2, 0x1b, 0x12, 0x0a,
	0x0a, 0x05, 0x45, 0x33, 0x35, 0x35, 0x35, 0x10, 0xe3, 0x1b, 0x12, 0x0a, 0x0a, 0x05, 0x45, 0x33,
	0x35, 0x35, 0x36, 0x10, 0xe4, 0x1b, 0x12, 0x18, 0x0a, 0x13, 0x45, 0x72, 0x72, 0x42, 0x61, 0x74,
	0x74, 0x6c, 0x65, 0x49, 0x64, 0x4e, 0x6f, 0x74, 0x45, 0x78, 0x69, 0x73, 0x74, 0x10, 0xa0, 0x1f,
	0x12, 0x13, 0x0a, 0x0e, 0x50, 0x65, 0x74, 0x43, 0x66, 0x67, 0x4e, 0x6f, 0x74, 0x45, 0x78, 0x69,
	0x74, 0x73, 0x10, 0x88, 0x27, 0x12, 0x14, 0x0a, 0x0f, 0x50, 0x65, 0x74, 0x54, 0x61, 0x6b, 0x65,
	0x4e, 0x75, 0x6d, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x10, 0x89, 0x27, 0x12, 0x0a, 0x0a, 0x05, 0x45,
	0x36, 0x30, 0x30, 0x30, 0x10, 0xf0, 0x2e, 0x12, 0x0a, 0x0a, 0x05, 0x45, 0x36, 0x30, 0x30, 0x31,
	0x10, 0xf1, 0x2e, 0x12, 0x0a, 0x0a, 0x05, 0x45, 0x36, 0x30, 0x30, 0x32, 0x10, 0xf2, 0x2e, 0x12,
	0x0a, 0x0a, 0x05, 0x45, 0x36, 0x30, 0x30, 0x33, 0x10, 0xf3, 0x2e, 0x12, 0x0a, 0x0a, 0x05, 0x45,
	0x36, 0x30, 0x30, 0x34, 0x10, 0xf4, 0x2e, 0x12, 0x0a, 0x0a, 0x05, 0x45, 0x36, 0x30, 0x30, 0x35,
	0x10, 0xf5, 0x2e, 0x12, 0x0a, 0x0a, 0x05, 0x45, 0x36, 0x30, 0x30, 0x36, 0x10, 0xf6, 0x2e, 0x42,
	0x27, 0x5a, 0x25, 0x77, 0x6f, 0x72, 0x6c, 0x64, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f,
	0x70, 0x62, 0x42, 0x61, 0x73, 0x65, 0x2f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x3b,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pbBase_Response_Response_proto_rawDescOnce sync.Once
	file_pbBase_Response_Response_proto_rawDescData = file_pbBase_Response_Response_proto_rawDesc
)

func file_pbBase_Response_Response_proto_rawDescGZIP() []byte {
	file_pbBase_Response_Response_proto_rawDescOnce.Do(func() {
		file_pbBase_Response_Response_proto_rawDescData = protoimpl.X.CompressGZIP(file_pbBase_Response_Response_proto_rawDescData)
	})
	return file_pbBase_Response_Response_proto_rawDescData
}

var file_pbBase_Response_Response_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_pbBase_Response_Response_proto_goTypes = []interface{}{
	(Code)(0), // 0: proto.Response.Code
}
var file_pbBase_Response_Response_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pbBase_Response_Response_proto_init() }
func file_pbBase_Response_Response_proto_init() {
	if File_pbBase_Response_Response_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pbBase_Response_Response_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbBase_Response_Response_proto_goTypes,
		DependencyIndexes: file_pbBase_Response_Response_proto_depIdxs,
		EnumInfos:         file_pbBase_Response_Response_proto_enumTypes,
	}.Build()
	File_pbBase_Response_Response_proto = out.File
	file_pbBase_Response_Response_proto_rawDesc = nil
	file_pbBase_Response_Response_proto_goTypes = nil
	file_pbBase_Response_Response_proto_depIdxs = nil
}
